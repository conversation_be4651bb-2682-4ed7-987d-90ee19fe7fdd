<?php
require_once '../auth_check.php';
require_once '../config/database.php';
require_once 'PlanosLogger.php';

// Verificar se o usuário é administrador
$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ?");
$stmt->execute([$_SESSION['user_id']]);
$usuario = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$usuario || !in_array($usuario['nivel_acesso_id'], [1, 2])) {
    header('Location: index.php?error=acesso_negado');
    exit;
}

$logger = new PlanosLogger($pdo);

// Parâmetros de filtro
$filtro_plano = $_GET['plano_id'] ?? '';
$filtro_usuario = $_GET['usuario_id'] ?? '';
$filtro_acao = $_GET['acao'] ?? '';
$filtro_data_inicio = $_GET['data_inicio'] ?? '';
$filtro_data_fim = $_GET['data_fim'] ?? '';
$limite = $_GET['limite'] ?? 50;

// Construir query de logs
$where_conditions = ["l.acao LIKE '%plano%' OR l.acao LIKE '%admin_%'"];
$params = [];

if (!empty($filtro_plano)) {
    $where_conditions[] = "l.plano_id = ?";
    $params[] = $filtro_plano;
}

if (!empty($filtro_usuario)) {
    $where_conditions[] = "l.usuario_id = ?";
    $params[] = $filtro_usuario;
}

if (!empty($filtro_acao)) {
    $where_conditions[] = "l.acao LIKE ?";
    $params[] = "%{$filtro_acao}%";
}

if (!empty($filtro_data_inicio)) {
    $where_conditions[] = "DATE(l.data_hora) >= ?";
    $params[] = $filtro_data_inicio;
}

if (!empty($filtro_data_fim)) {
    $where_conditions[] = "DATE(l.data_hora) <= ?";
    $params[] = $filtro_data_fim;
}

$where_clause = implode(' AND ', $where_conditions);

try {
    $sql = "
        SELECT l.*, u.nome_completo as usuario_nome, p.nome as plano_nome
        FROM logs l
        LEFT JOIN usuarios u ON l.usuario_id = u.id
        LEFT JOIN planos p ON l.plano_id = p.id
        WHERE {$where_clause}
        ORDER BY l.data_hora DESC
        LIMIT ?
    ";
    
    $params[] = (int)$limite;
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Buscar total de registros
    $sql_count = "
        SELECT COUNT(*) as total
        FROM logs l
        WHERE {$where_clause}
    ";
    $stmt_count = $pdo->prepare($sql_count);
    $stmt_count->execute(array_slice($params, 0, -1)); // Remove o limite
    $total_logs = $stmt_count->fetch()['total'];
    
} catch (Exception $e) {
    $logs = [];
    $total_logs = 0;
    $erro = "Erro ao buscar logs: " . $e->getMessage();
}

// Buscar usuários para filtro
$stmt = $pdo->query("SELECT id, nome_completo FROM usuarios WHERE ativo = 1 ORDER BY nome_completo");
$usuarios = $stmt->fetchAll();

// Buscar planos para filtro
$stmt = $pdo->query("SELECT id, nome FROM planos ORDER BY nome LIMIT 100");
$planos = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Logs dos Planos - Sistema Sicoob</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- CSS Personalizado -->
    <link rel="stylesheet" href="../assets/css/sicoob-style.css">
    
    <style>
        .admin-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        
        .filter-section {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .logs-section {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .log-item {
            border-left: 4px solid #00A091;
            padding: 1rem;
            margin-bottom: 1rem;
            background: #f8f9fa;
            border-radius: 0 8px 8px 0;
        }
        
        .log-action {
            font-weight: 600;
            color: #003641;
        }
        
        .log-details {
            background: white;
            padding: 0.75rem;
            border-radius: 6px;
            margin-top: 0.5rem;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .btn-sicoob {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border: none;
            color: white;
            font-weight: 600;
        }
        
        .btn-sicoob:hover {
            background: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-color) 100%);
            color: white;
        }
        
        .badge-acao {
            font-size: 0.75rem;
            padding: 0.4rem 0.8rem;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="admin-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-0">
                        <i class="fas fa-history me-3"></i>
                        Logs dos Planos de Ação
                    </h1>
                    <p class="mb-0 mt-2 opacity-75">Histórico completo de ações realizadas no sistema</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="index.php" class="btn btn-light">
                        <i class="fas fa-arrow-left me-2"></i>Voltar aos Planos
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Filtros -->
        <div class="filter-section">
            <h3 class="mb-4">
                <i class="fas fa-filter me-2"></i>Filtros
            </h3>
            
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="plano_id" class="form-label">Plano</label>
                    <select class="form-select" id="plano_id" name="plano_id">
                        <option value="">Todos os planos</option>
                        <?php foreach ($planos as $plano): ?>
                        <option value="<?php echo $plano['id']; ?>" <?php echo ($filtro_plano == $plano['id']) ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($plano['nome']); ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="col-md-3">
                    <label for="usuario_id" class="form-label">Usuário</label>
                    <select class="form-select" id="usuario_id" name="usuario_id">
                        <option value="">Todos os usuários</option>
                        <?php foreach ($usuarios as $user): ?>
                        <option value="<?php echo $user['id']; ?>" <?php echo ($filtro_usuario == $user['id']) ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($user['nome_completo']); ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label for="acao" class="form-label">Ação</label>
                    <select class="form-select" id="acao" name="acao">
                        <option value="">Todas as ações</option>
                        <option value="criar_plano" <?php echo ($filtro_acao == 'criar_plano') ? 'selected' : ''; ?>>Criar Plano</option>
                        <option value="editar_plano" <?php echo ($filtro_acao == 'editar_plano') ? 'selected' : ''; ?>>Editar Plano</option>
                        <option value="mudar_status" <?php echo ($filtro_acao == 'mudar_status') ? 'selected' : ''; ?>>Mudar Status</option>
                        <option value="admin_" <?php echo ($filtro_acao == 'admin_') ? 'selected' : ''; ?>>Ações Admin</option>
                    </select>
                </div>
                
                <div class="col-md-2">
                    <label for="data_inicio" class="form-label">Data Início</label>
                    <input type="date" class="form-control" id="data_inicio" name="data_inicio" value="<?php echo $filtro_data_inicio; ?>">
                </div>
                
                <div class="col-md-2">
                    <label for="data_fim" class="form-label">Data Fim</label>
                    <input type="date" class="form-control" id="data_fim" name="data_fim" value="<?php echo $filtro_data_fim; ?>">
                </div>
                
                <div class="col-12">
                    <button type="submit" class="btn btn-sicoob">
                        <i class="fas fa-search me-2"></i>Filtrar
                    </button>
                    <a href="logs.php" class="btn btn-secondary ms-2">
                        <i class="fas fa-times me-2"></i>Limpar Filtros
                    </a>
                    
                    <div class="float-end">
                        <select name="limite" class="form-select d-inline-block w-auto" onchange="this.form.submit()">
                            <option value="25" <?php echo ($limite == 25) ? 'selected' : ''; ?>>25 registros</option>
                            <option value="50" <?php echo ($limite == 50) ? 'selected' : ''; ?>>50 registros</option>
                            <option value="100" <?php echo ($limite == 100) ? 'selected' : ''; ?>>100 registros</option>
                            <option value="200" <?php echo ($limite == 200) ? 'selected' : ''; ?>>200 registros</option>
                        </select>
                    </div>
                </div>
            </form>
        </div>

        <!-- Lista de Logs -->
        <div class="logs-section">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h3 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    Registros de Log (<?php echo number_format($total_logs); ?> total)
                </h3>
                
                <div>
                    <span class="text-muted">Mostrando <?php echo count($logs); ?> de <?php echo number_format($total_logs); ?> registros</span>
                </div>
            </div>
            
            <?php if (empty($logs)): ?>
            <div class="text-center py-5">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Nenhum log encontrado</h5>
                <p class="text-muted">Tente ajustar os filtros para encontrar os registros desejados.</p>
            </div>
            <?php else: ?>
            
            <?php foreach ($logs as $log): ?>
            <div class="log-item">
                <div class="row">
                    <div class="col-md-8">
                        <div class="d-flex align-items-center mb-2">
                            <span class="badge badge-acao bg-primary me-2">
                                <?php echo htmlspecialchars($log['acao']); ?>
                            </span>
                            
                            <?php if ($log['plano_nome']): ?>
                            <span class="badge bg-secondary me-2">
                                Plano: <?php echo htmlspecialchars($log['plano_nome']); ?>
                            </span>
                            <?php endif; ?>
                        </div>
                        
                        <div class="log-action mb-1">
                            <i class="fas fa-user me-1"></i>
                            <?php echo htmlspecialchars($log['usuario_nome'] ?: 'Usuário não encontrado'); ?>
                        </div>
                        
                        <?php if ($log['detalhes']): ?>
                        <div class="log-details">
                            <?php 
                            $detalhes = $log['detalhes'];
                            // Tentar decodificar JSON para exibição mais bonita
                            $json_decoded = json_decode($detalhes, true);
                            if ($json_decoded) {
                                echo '<pre>' . htmlspecialchars(json_encode($json_decoded, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . '</pre>';
                            } else {
                                echo htmlspecialchars($detalhes);
                            }
                            ?>
                        </div>
                        <?php endif; ?>
                    </div>
                    
                    <div class="col-md-4 text-end">
                        <div class="text-muted">
                            <i class="fas fa-clock me-1"></i>
                            <?php echo date('d/m/Y H:i:s', strtotime($log['data_hora'])); ?>
                        </div>
                        
                        <?php if ($log['ip_usuario']): ?>
                        <div class="text-muted mt-1">
                            <i class="fas fa-globe me-1"></i>
                            <?php echo htmlspecialchars($log['ip_usuario']); ?>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
            
            <?php endif; ?>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
