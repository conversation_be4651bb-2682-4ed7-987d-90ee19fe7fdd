<?php
require_once '../auth_check.php';
require_once '../config/database.php';
require_once 'PlanosLogger.php';

// Verificar se o usuário está logado
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Usuário não autenticado']);
    exit;
}

// Verificar se é uma requisição POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Método não permitido']);
    exit;
}

// Verificar se os dados necessários foram enviados
if (!isset($_POST['plano_id']) || !isset($_POST['novo_status_id'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Dados incompletos']);
    exit;
}

$plano_id = (int)$_POST['plano_id'];
$novo_status_id = (int)$_POST['novo_status_id'];
$observacao = $_POST['observacao'] ?? '';

try {
    // Buscar o status atual do plano
    $stmt = $pdo->prepare("SELECT status_id FROM planos WHERE id = ?");
    $stmt->execute([$plano_id]);
    $plano = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$plano) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'Plano não encontrado']);
        exit;
    }
    
    $status_anterior_id = $plano['status_id'];
    
    // Verificar se o novo status existe
    $stmt = $pdo->prepare("SELECT nome FROM planos_status WHERE id = ? AND ativo = 1");
    $stmt->execute([$novo_status_id]);
    $novo_status = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$novo_status) {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => 'Status inválido']);
        exit;
    }
    
    // Verificar se o status realmente mudou
    if ($status_anterior_id == $novo_status_id) {
        echo json_encode(['success' => true, 'message' => 'Status já está atualizado']);
        exit;
    }
    
    // Iniciar transação
    $pdo->beginTransaction();
    
    // Atualizar o status do plano
    $stmt = $pdo->prepare("UPDATE planos SET status_id = ?, updated_at = NOW() WHERE id = ?");
    $stmt->execute([$novo_status_id, $plano_id]);
    
    // Registrar no histórico de status
    $stmt = $pdo->prepare("
        INSERT INTO planos_historico_status (plano_id, status_anterior_id, status_novo_id, usuario_id, observacao, created_at)
        VALUES (?, ?, ?, ?, ?, NOW())
    ");
    $stmt->execute([$plano_id, $status_anterior_id, $novo_status_id, $_SESSION['user_id'], $observacao]);
    
    // Buscar nomes dos status para a interação
    $stmt = $pdo->prepare("SELECT nome FROM planos_status WHERE id = ?");
    $stmt->execute([$status_anterior_id]);
    $status_anterior_nome = $stmt->fetch(PDO::FETCH_ASSOC)['nome'] ?? 'Desconhecido';
    
    // Criar interação sobre a mudança de status
    $conteudo_interacao = "Status alterado de '{$status_anterior_nome}' para '{$novo_status['nome']}'";
    if (!empty($observacao)) {
        $conteudo_interacao .= "\n\nObservação: " . $observacao;
    }
    
    $stmt = $pdo->prepare("
        INSERT INTO planos_interacoes (plano_id, usuario_id, tipo, conteudo, created_at)
        VALUES (?, ?, 'atualizacao', ?, NOW())
    ");
    $stmt->execute([$plano_id, $_SESSION['user_id'], $conteudo_interacao]);

    // Registrar log da mudança de status
    $logger = new PlanosLogger($pdo);
    $logger->logMudarStatus($_SESSION['user_id'], $plano_id, $status_anterior_id, $novo_status_id, $observacao);

    // Confirmar transação
    $pdo->commit();
    
    // Buscar dados atualizados do plano para retornar
    $stmt = $pdo->prepare("
        SELECT p.*, ps.nome as status_nome, ps.cor as status_cor, ps.icone as status_icone
        FROM planos p
        LEFT JOIN planos_status ps ON p.status_id = ps.id
        WHERE p.id = ?
    ");
    $stmt->execute([$plano_id]);
    $plano_atualizado = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'message' => 'Status alterado com sucesso!',
        'plano' => $plano_atualizado,
        'redirect' => "visualizar.php?id={$plano_id}&success=status_alterado"
    ]);
    
} catch (Exception $e) {
    // Reverter transação em caso de erro
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    http_response_code(500);
    echo json_encode([
        'success' => false, 
        'message' => 'Erro ao alterar status: ' . $e->getMessage()
    ]);
}
?>
