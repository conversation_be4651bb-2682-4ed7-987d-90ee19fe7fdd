<?php
/**
 * Classe para registrar logs das ações dos planos de ação
 */
class PlanosLogger {
    private $pdo;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    /**
     * Registra uma ação no log
     * 
     * @param int $usuario_id ID do usuário que executou a ação
     * @param string $acao Tipo da ação (criar_plano, editar_plano, etc.)
     * @param string $detalhes Detalhes da ação em formato JSON ou texto
     * @param int|null $plano_id ID do plano relacionado (opcional)
     * @param string|null $ip_usuario IP do usuário (opcional)
     * @return bool True se o log foi registrado com sucesso
     */
    public function registrarLog($usuario_id, $acao, $detalhes, $plano_id = null, $ip_usuario = null) {
        try {
            // Se não foi fornecido IP, tentar obter automaticamente
            if ($ip_usuario === null) {
                $ip_usuario = $this->obterIpUsuario();
            }
            
            // Preparar detalhes como JSON se for array
            if (is_array($detalhes)) {
                $detalhes = json_encode($detalhes, JSON_UNESCAPED_UNICODE);
            }
            
            $stmt = $this->pdo->prepare("
                INSERT INTO logs (usuario_id, acao, detalhes, plano_id, ip_usuario, created_at) 
                VALUES (?, ?, ?, ?, ?, NOW())
            ");
            
            return $stmt->execute([
                $usuario_id,
                $acao,
                $detalhes,
                $plano_id,
                $ip_usuario
            ]);
            
        } catch (Exception $e) {
            // Log do erro no error_log do PHP para não quebrar a aplicação
            error_log("Erro ao registrar log de planos: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Registra criação de plano
     */
    public function logCriarPlano($usuario_id, $plano_id, $dados_plano) {
        $detalhes = [
            'plano_id' => $plano_id,
            'nome' => $dados_plano['nome'] ?? '',
            'status_id' => $dados_plano['status_id'] ?? null,
            'legenda_id' => $dados_plano['legenda_id'] ?? null,
            'quando_inicio' => $dados_plano['quando_inicio'] ?? null,
            'quando_fim' => $dados_plano['quando_fim'] ?? null
        ];
        
        return $this->registrarLog(
            $usuario_id, 
            'criar_plano', 
            $detalhes, 
            $plano_id
        );
    }
    
    /**
     * Registra edição de plano
     */
    public function logEditarPlano($usuario_id, $plano_id, $dados_anteriores, $dados_novos) {
        $detalhes = [
            'plano_id' => $plano_id,
            'dados_anteriores' => $dados_anteriores,
            'dados_novos' => $dados_novos,
            'campos_alterados' => $this->identificarCamposAlterados($dados_anteriores, $dados_novos)
        ];
        
        return $this->registrarLog(
            $usuario_id, 
            'editar_plano', 
            $detalhes, 
            $plano_id
        );
    }
    
    /**
     * Registra mudança de status
     */
    public function logMudarStatus($usuario_id, $plano_id, $status_anterior, $status_novo, $observacao = null) {
        $detalhes = [
            'plano_id' => $plano_id,
            'status_anterior_id' => $status_anterior,
            'status_novo_id' => $status_novo,
            'observacao' => $observacao
        ];
        
        return $this->registrarLog(
            $usuario_id, 
            'mudar_status_plano', 
            $detalhes, 
            $plano_id
        );
    }
    
    /**
     * Registra exclusão de plano
     */
    public function logExcluirPlano($usuario_id, $plano_id, $dados_plano) {
        $detalhes = [
            'plano_id' => $plano_id,
            'nome' => $dados_plano['nome'] ?? '',
            'dados_completos' => $dados_plano
        ];
        
        return $this->registrarLog(
            $usuario_id, 
            'excluir_plano', 
            $detalhes, 
            $plano_id
        );
    }
    
    /**
     * Registra adição de responsável
     */
    public function logAdicionarResponsavel($usuario_id, $plano_id, $responsavel_dados) {
        $detalhes = [
            'plano_id' => $plano_id,
            'responsavel' => $responsavel_dados
        ];
        
        return $this->registrarLog(
            $usuario_id, 
            'adicionar_responsavel', 
            $detalhes, 
            $plano_id
        );
    }
    
    /**
     * Registra remoção de responsável
     */
    public function logRemoverResponsavel($usuario_id, $plano_id, $responsavel_dados) {
        $detalhes = [
            'plano_id' => $plano_id,
            'responsavel' => $responsavel_dados
        ];
        
        return $this->registrarLog(
            $usuario_id, 
            'remover_responsavel', 
            $detalhes, 
            $plano_id
        );
    }
    
    /**
     * Registra adição de interação/comentário
     */
    public function logAdicionarInteracao($usuario_id, $plano_id, $tipo_interacao, $titulo, $conteudo) {
        $detalhes = [
            'plano_id' => $plano_id,
            'tipo' => $tipo_interacao,
            'titulo' => $titulo,
            'conteudo' => substr($conteudo, 0, 200) // Limitar tamanho
        ];
        
        return $this->registrarLog(
            $usuario_id, 
            'adicionar_interacao', 
            $detalhes, 
            $plano_id
        );
    }
    
    /**
     * Registra ações administrativas
     */
    public function logAcaoAdmin($usuario_id, $acao, $detalhes_acao) {
        return $this->registrarLog(
            $usuario_id, 
            'admin_' . $acao, 
            $detalhes_acao
        );
    }
    
    /**
     * Obtém o IP do usuário
     */
    private function obterIpUsuario() {
        // Verificar se está atrás de proxy
        if (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
        } elseif (!empty($_SERVER['HTTP_X_REAL_IP'])) {
            $ip = $_SERVER['HTTP_X_REAL_IP'];
        } elseif (!empty($_SERVER['REMOTE_ADDR'])) {
            $ip = $_SERVER['REMOTE_ADDR'];
        } else {
            $ip = 'unknown';
        }
        
        // Se há múltiplos IPs (proxy chain), pegar o primeiro
        if (strpos($ip, ',') !== false) {
            $ip = trim(explode(',', $ip)[0]);
        }
        
        return $ip;
    }
    
    /**
     * Identifica quais campos foram alterados
     */
    private function identificarCamposAlterados($dados_anteriores, $dados_novos) {
        $campos_alterados = [];
        
        foreach ($dados_novos as $campo => $valor_novo) {
            $valor_anterior = $dados_anteriores[$campo] ?? null;
            
            // Comparar valores (convertendo para string para evitar problemas de tipo)
            if ((string)$valor_anterior !== (string)$valor_novo) {
                $campos_alterados[] = $campo;
            }
        }
        
        return $campos_alterados;
    }
    
    /**
     * Busca logs de um plano específico
     */
    public function buscarLogsPorPlano($plano_id, $limite = 50) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT l.*, u.nome_completo as usuario_nome
                FROM logs l
                LEFT JOIN usuarios u ON l.usuario_id = u.id
                WHERE l.plano_id = ?
                ORDER BY l.created_at DESC
                LIMIT ?
            ");
            
            $stmt->execute([$plano_id, $limite]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log("Erro ao buscar logs do plano {$plano_id}: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Busca logs por usuário
     */
    public function buscarLogsPorUsuario($usuario_id, $limite = 50) {
        try {
            $stmt = $this->pdo->prepare("
                SELECT l.*, u.nome_completo as usuario_nome
                FROM logs l
                LEFT JOIN usuarios u ON l.usuario_id = u.id
                WHERE l.usuario_id = ? AND l.acao LIKE '%plano%'
                ORDER BY l.created_at DESC
                LIMIT ?
            ");
            
            $stmt->execute([$usuario_id, $limite]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            error_log("Erro ao buscar logs do usuário {$usuario_id}: " . $e->getMessage());
            return [];
        }
    }
}
?>
