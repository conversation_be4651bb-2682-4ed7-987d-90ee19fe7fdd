<?php
session_start();
require_once '../config/database.php';
require_once 'PlanosLogger.php';

// Verificar se o usuário está logado
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit();
}

// Buscar dados do usuário logado - versão simplificada para debug
$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ?");
$stmt->execute([$_SESSION['user_id']]);
$usuario = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$usuario) {
    header('Location: ../login.php');
    exit;
}



// Verificação de permissão (mesmo sistema do index.php)
$tem_permissao = false;

// Verificação alternativa: usuários com nível de acesso 1 (admin) ou 2 (gestor) têm acesso
if ($usuario['nivel_acesso_id'] && in_array($usuario['nivel_acesso_id'], [1, 2])) {
    $tem_permissao = true;
} else {
    // Verificar permissões de acesso aos planos (usuário direto ou por setor)
    $tem_permissao = false;

    try {
        // 1. Verificar se tem acesso direto por usuário a qualquer botão
        $stmt = $pdo->prepare("
            SELECT COUNT(*)
            FROM card_button_usuarios cbu
            WHERE cbu.usuario_id = ?
        ");
        $stmt->execute([$_SESSION['user_id']]);
        $tem_permissao_usuario = $stmt->fetchColumn() > 0;

        if ($tem_permissao_usuario) {
            $tem_permissao = true;
        } else {
            // 2. Verificar se tem acesso por setor a qualquer botão
            $stmt = $pdo->prepare("
                SELECT COUNT(*)
                FROM card_button_setores cbs
                INNER JOIN usuario_setor us ON cbs.setor_id = us.setor_id
                WHERE us.usuario_id = ?
            ");
            $stmt->execute([$_SESSION['user_id']]);
            $tem_permissao_setor = $stmt->fetchColumn() > 0;

            if ($tem_permissao_setor) {
                $tem_permissao = true;
            }
        }

    } catch (Exception $e) {
        // Se der erro na consulta, tentar com nome de coluna alternativo
        try {
            $stmt = $pdo->prepare("
                SELECT COUNT(*)
                FROM card_button_usuarios cbu
                WHERE cbu.user_id = ?
            ");
            $stmt->execute([$_SESSION['user_id']]);
            $tem_permissao = $stmt->fetchColumn() > 0;
        } catch (Exception $e2) {
            // Se ainda der erro, permitir acesso para usuários logados
            $tem_permissao = true;
        }
    }
}

if (!$tem_permissao) {
    header('Location: ../dashboard.php');
    exit();
}

// Buscar dados para os selects
$stmt_status = $pdo->query("SELECT * FROM planos_status WHERE ativo = 1 ORDER BY ordem");
$status_list = $stmt_status->fetchAll();

// Buscar legendas disponíveis
$stmt_legendas = $pdo->query("SELECT * FROM planos_legendas WHERE ativo = 1 ORDER BY ordem, nome");
$legendas_list = $stmt_legendas->fetchAll();

// Buscar todos os usuários e grupos diretamente das tabelas
$responsaveis_list = [];

try {
    // Buscar usuários ativos
    $stmt_usuarios = $pdo->query("
        SELECT id, nome_completo as nome, email, 'usuario' as tipo, id as original_id
        FROM usuarios
        WHERE ativo = true
        ORDER BY nome_completo
    ");
    $usuarios = $stmt_usuarios->fetchAll();

    foreach ($usuarios as $usuario) {
        $responsaveis_list[] = [
            'id' => 'user_' . $usuario['id'],
            'nome' => $usuario['nome'],
            'email' => $usuario['email'],
            'tipo' => 'usuario',
            'original_id' => $usuario['id'],
            'usuario_id' => $usuario['id'],
            'grupo_id' => null
        ];
    }

    // Buscar grupos ativos de planos
    $stmt_grupos = $pdo->query("
        SELECT id, nome, descricao, cor, 'grupo' as tipo, id as original_id
        FROM planos_grupos
        WHERE ativo = true
        ORDER BY nome
    ");
    $grupos = $stmt_grupos->fetchAll();

    foreach ($grupos as $grupo) {
        $responsaveis_list[] = [
            'id' => 'grupo_' . $grupo['id'],
            'nome' => $grupo['nome'],
            'email' => null,
            'tipo' => 'grupo',
            'original_id' => $grupo['id'],
            'usuario_id' => null,
            'grupo_id' => $grupo['id'],
            'descricao' => $grupo['descricao'],
            'cor' => $grupo['cor']
        ];
    }

} catch (Exception $e) {
    // Em caso de erro, manter array vazio
    $responsaveis_list = [];
}

// Processar formulário

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $pdo->beginTransaction();
        
        // Validar campos obrigatórios
        $required_fields = ['nome', 'oque', 'porque', 'quando_inicio', 'quando_fim', 'como', 'status_id'];
        foreach ($required_fields as $field) {
            if (empty($_POST[$field])) {
                throw new Exception("Campo obrigatório não preenchido: " . $field);
            }
        }
        
        // Validar datas
        $inicio = new DateTime($_POST['quando_inicio']);
        $fim = new DateTime($_POST['quando_fim']);
        if ($fim < $inicio) {
            throw new Exception("A data de fim deve ser posterior à data de início.");
        }
        
        // Inserir plano
        $stmt = $pdo->prepare("
            INSERT INTO planos (nome, oque, porque, onde, quando_inicio, quando_fim, como, quanto_custa, status_id, legenda_id, criado_por)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        // Processar valor monetário
        $valor_monetario = 0;
        if (!empty($_POST['quanto_custa_value'])) {
            $valor_monetario = floatval($_POST['quanto_custa_value']);
        } elseif (!empty($_POST['quanto_custa'])) {
            // Fallback para compatibilidade
            $valor_str = str_replace(['.', ','], ['', '.'], $_POST['quanto_custa']);
            $valor_monetario = floatval($valor_str);
        }

        $stmt->execute([
            $_POST['nome'],
            $_POST['oque'],
            $_POST['porque'],
            $_POST['onde'] ?? null,
            $_POST['quando_inicio'],
            $_POST['quando_fim'],
            $_POST['como'],
            $valor_monetario,
            $_POST['status_id'],
            !empty($_POST['legenda_id']) ? $_POST['legenda_id'] : null,
            $_SESSION['user_id']
        ]);
        
        $plano_id = $pdo->lastInsertId();

        // Registrar log da criação do plano
        $logger = new PlanosLogger($pdo);
        $dados_plano = [
            'nome' => $_POST['nome'],
            'status_id' => $_POST['status_id'],
            'legenda_id' => !empty($_POST['legenda_id']) ? $_POST['legenda_id'] : null,
            'quando_inicio' => $_POST['quando_inicio'],
            'quando_fim' => $_POST['quando_fim'],
            'quanto_custa' => $valor_monetario
        ];
        $logger->logCriarPlano($_SESSION['user_id'], $plano_id, $dados_plano);

        // Inserir responsáveis
        if (!empty($_POST['responsaveis']) && is_array($_POST['responsaveis'])) {
            foreach ($_POST['responsaveis'] as $responsavel_id) {
                // Verificar se é usuário ou setor baseado no prefixo
                if (strpos($responsavel_id, 'user_') === 0) {
                    // É um usuário
                    $original_id = str_replace('user_', '', $responsavel_id);

                    // Verificar se já existe na tabela planos_responsaveis
                    $stmt_check = $pdo->prepare("
                        SELECT id FROM planos_responsaveis
                        WHERE tipo = 'usuario' AND usuario_id = ?
                    ");
                    $stmt_check->execute([$original_id]);
                    $existing = $stmt_check->fetch();

                    if (!$existing) {
                        // Buscar dados do usuário
                        $stmt_user = $pdo->prepare("SELECT nome_completo, email FROM usuarios WHERE id = ?");
                        $stmt_user->execute([$original_id]);
                        $user_data = $stmt_user->fetch();

                        if ($user_data) {
                            // Inserir na tabela planos_responsaveis
                            $stmt_insert = $pdo->prepare("
                                INSERT INTO planos_responsaveis (tipo, nome, email, usuario_id)
                                VALUES ('usuario', ?, ?, ?)
                            ");
                            $stmt_insert->execute([$user_data['nome_completo'], $user_data['email'], $original_id]);
                            $responsavel_db_id = $pdo->lastInsertId();
                        }
                    } else {
                        $responsavel_db_id = $existing['id'];
                    }

                } elseif (strpos($responsavel_id, 'setor_') === 0) {
                    // É um setor
                    $original_id = str_replace('setor_', '', $responsavel_id);

                    // Verificar se já existe na tabela planos_responsaveis
                    $stmt_check = $pdo->prepare("
                        SELECT id FROM planos_responsaveis
                        WHERE tipo = 'setor' AND setor_id = ?
                    ");
                    $stmt_check->execute([$original_id]);
                    $existing = $stmt_check->fetch();

                    if (!$existing) {
                        // Buscar dados do setor
                        $stmt_setor = $pdo->prepare("SELECT nome FROM setores WHERE id = ?");
                        $stmt_setor->execute([$original_id]);
                        $setor_data = $stmt_setor->fetch();

                        if ($setor_data) {
                            // Inserir na tabela planos_responsaveis
                            $stmt_insert = $pdo->prepare("
                                INSERT INTO planos_responsaveis (tipo, nome, setor_id)
                                VALUES ('setor', ?, ?)
                            ");
                            $stmt_insert->execute([$setor_data['nome'], $original_id]);
                            $responsavel_db_id = $pdo->lastInsertId();
                        }
                    } else {
                        $responsavel_db_id = $existing['id'];
                    }
                }

                // Inserir vínculo se temos um ID válido
                if (isset($responsavel_db_id)) {
                    $stmt_vinculo = $pdo->prepare("
                        INSERT INTO planos_responsaveis_vinculo (plano_id, responsavel_id, papel)
                        VALUES (?, ?, ?)
                    ");
                    $stmt_vinculo->execute([$plano_id, $responsavel_db_id, 'Responsável']);
                }
            }
        }
        
        // Registrar histórico de status
        $stmt_hist = $pdo->prepare("
            INSERT INTO planos_historico_status (plano_id, status_novo_id, usuario_id, observacao)
            VALUES (?, ?, ?, ?)
        ");
        $stmt_hist->execute([$plano_id, $_POST['status_id'], $_SESSION['user_id'], 'Plano criado']);
        
        // Inserir interação inicial se houver resultados
        if (!empty($_POST['resultados'])) {
            $stmt_inter = $pdo->prepare("
                INSERT INTO planos_interacoes (plano_id, usuario_id, tipo, titulo, conteudo)
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt_inter->execute([$plano_id, $_SESSION['user_id'], 'comentario', 'Resultados Esperados', $_POST['resultados']]);
        }
        
        $pdo->commit();

        // Redirecionar para index.php com mensagem de sucesso
        header('Location: index.php?success=plano_criado&plano_id=' . $plano_id);
        exit();
        
    } catch (Exception $e) {
        $pdo->rollBack();
        // Redirecionar para index.php com mensagem de erro
        header('Location: index.php?error=' . urlencode($e->getMessage()));
        exit();
    }
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Novo Plano de Ação - Sicoob</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            /* Cores oficiais Sicoob */
            --sicoob-turquesa: #00A091;        /* RGB: 0, 160, 145 - Turquesa */
            --sicoob-verde-escuro: #003641;    /* RGB: 0, 54, 65 - Verde escuro */
            --sicoob-verde-medio: #7DB61C;     /* RGB: 125, 182, 28 - Verde médio */
            --sicoob-verde-claro: #C9D200;     /* RGB: 201, 210, 0 - Verde claro */
            --sicoob-roxo: #49479D;            /* RGB: 73, 71, 157 - Roxo */
            --sicoob-branco: #FFFFFF;          /* RGB: 255, 255, 255 - Branco */

            /* Cores de Sistema baseadas na identidade oficial */
            --primary-color: var(--sicoob-turquesa);
            --secondary-color: var(--sicoob-verde-escuro);
            --accent-color: var(--sicoob-verde-medio);
            --accent-light: var(--sicoob-verde-claro);
            --accent-purple: var(--sicoob-roxo);
            --success-color: var(--sicoob-verde-medio);
            --warning-color: var(--sicoob-roxo);
            --danger-color: #D32F2F;
            --info-color: var(--sicoob-turquesa);
            --dark-color: var(--sicoob-verde-escuro);
            --light-color: #F8FFFE;
            --white-color: var(--sicoob-branco);
            --gray-color: #6B7280;
        }

        body {
            background: linear-gradient(135deg, var(--light-color) 0%, rgba(0, 160, 145, 0.03) 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .navbar {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            box-shadow: 0 4px 20px rgba(0, 54, 65, 0.2);
            border-bottom: 3px solid var(--accent-color);
            position: relative;
            padding: 15px 0;
        }

        .navbar-brand {
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0;
        }

        .navbar-brand-img {
            filter: brightness(1.1);
        }

        /* Estilo do botão menu igual ao dashboard */
        .btn-menu-sicoob {
            background: rgba(255, 255, 255, 0.15);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .btn-menu-sicoob:hover {
            background: rgba(255, 255, 255, 0.25);
            border-color: rgba(255, 255, 255, 0.5);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        .btn-menu-sicoob:active {
            transform: translateY(0);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .btn-menu-sicoob i {
            font-size: 0.9rem;
        }

        .main-container {
            padding: 40px 0;
        }

        .form-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 8px 30px rgba(0,0,0,0.1);
            border-left: 5px solid var(--primary-color);
            overflow: hidden;
        }

        .form-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 25px 30px;
            margin: 0;
        }

        .form-body {
            padding: 30px;
        }

        .form-section {
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(0, 160, 145, 0.02);
            border-radius: 10px;
            border-left: 3px solid var(--primary-color);
        }

        .section-title {
            color: var(--secondary-color);
            font-weight: 600;
            font-size: 1.1rem;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .section-title i {
            margin-right: 10px;
            color: var(--primary-color);
        }

        .form-control, .form-select {
            border: 2px solid rgba(0, 160, 145, 0.2);
            border-radius: 8px;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.25rem rgba(0, 160, 145, 0.15);
        }

        .btn-sicoob {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border: none;
            color: white;
            font-weight: 600;
            padding: 12px 30px;
            border-radius: 25px;
            transition: all 0.3s ease;
        }

        .btn-sicoob:hover {
            background: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-color) 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 160, 145, 0.4);
            color: white;
        }

        .btn-outline-sicoob {
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
            background: transparent;
            font-weight: 600;
            padding: 12px 30px;
            border-radius: 25px;
            transition: all 0.3s ease;
        }

        .btn-outline-sicoob:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }

        .alert-sicoob-success {
            background-color: rgba(125, 182, 28, 0.1);
            border: 1px solid var(--accent-color);
            color: var(--secondary-color);
            border-radius: 8px;
        }

        .alert-sicoob-danger {
            background-color: rgba(211, 47, 47, 0.1);
            border: 1px solid var(--danger-color);
            color: var(--danger-color);
            border-radius: 8px;
        }

        .responsaveis-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 10px;
            max-height: 200px;
            overflow-y: auto;
            padding: 10px;
            border: 1px solid rgba(0, 160, 145, 0.2);
            border-radius: 8px;
            background: rgba(0, 160, 145, 0.02);
        }

        .responsavel-item {
            display: flex;
            align-items: center;
            padding: 8px 12px;
            background: white;
            border-radius: 6px;
            border: 1px solid rgba(0, 160, 145, 0.1);
            transition: all 0.2s ease;
        }

        .responsavel-item:hover {
            background: rgba(0, 160, 145, 0.05);
            border-color: var(--primary-color);
        }

        .responsavel-item input[type="checkbox"] {
            margin-right: 8px;
        }

        .responsavel-tipo {
            font-size: 0.75rem;
            padding: 2px 6px;
            border-radius: 10px;
            margin-left: auto;
        }

        .tipo-usuario {
            background: rgba(0, 160, 145, 0.1);
            color: var(--primary-color);
        }

        .tipo-setor {
            background: rgba(73, 71, 157, 0.1);
            color: var(--accent-purple);
        }

        /* Estilos para responsáveis selecionados */
        .responsavel-selecionado {
            display: inline-flex;
            align-items: center;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
            margin: 2px;
        }

        .responsavel-selecionado .btn-remove {
            background: none;
            border: none;
            color: white;
            margin-left: 8px;
            padding: 0;
            font-size: 0.8rem;
            cursor: pointer;
            opacity: 0.8;
            transition: opacity 0.2s ease;
        }

        .responsavel-selecionado .btn-remove:hover {
            opacity: 1;
        }

        /* Ocultar itens não encontrados na busca */
        .responsavel-item.hidden {
            display: none;
        }

        /* Responsividade */
        @media (max-width: 768px) {
            .main-container {
                padding: 20px 0;
            }
            
            .form-body {
                padding: 20px;
            }
            
            .responsaveis-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar - Identidade Visual Oficial Sicoob -->
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid px-4">
            <div class="d-flex justify-content-between align-items-center w-100">
                <!-- Logo e Título à Esquerda -->
                <div class="d-flex align-items-center">
                    <img src="../assets/images/logo-sicoob.png" alt="Sicoob" class="navbar-brand-img me-3" style="height: 40px;">
                    <span class="navbar-brand mb-0 h1 text-white">
                        <i class="fas fa-plus-circle me-2"></i>Novo Plano de Ação
                    </span>
                </div>

                <!-- Usuário e Menu à Direita -->
                <div class="d-flex align-items-center">
                    <span class="text-white me-3">
                        <i class="fas fa-user-circle me-1"></i>
                        Olá, <?php
                            // Buscar nome com múltiplos fallbacks
                            $nome_usuario = 'Usuário';

                            if (isset($usuario['nome_completo']) && !empty($usuario['nome_completo'])) {
                                $nome_usuario = $usuario['nome_completo'];
                            } elseif (isset($usuario['nome']) && !empty($usuario['nome'])) {
                                $nome_usuario = $usuario['nome'];
                            } elseif (isset($usuario['username']) && !empty($usuario['username'])) {
                                $nome_usuario = $usuario['username'];
                            } elseif (isset($_SESSION['nome_completo']) && !empty($_SESSION['nome_completo'])) {
                                $nome_usuario = $_SESSION['nome_completo'];
                            } elseif (isset($_SESSION['username']) && !empty($_SESSION['username'])) {
                                $nome_usuario = $_SESSION['username'];
                            }

                            echo htmlspecialchars($nome_usuario);
                        ?>
                    </span>

                    <a href="index.php" class="btn btn-menu-sicoob me-2" title="Voltar aos Planos">
                        <i class="fas fa-arrow-left me-2"></i>
                        <span>Voltar</span>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid main-container">
        <div class="row justify-content-center">
            <div class="col-12 col-lg-10 col-xl-8">



                <!-- Formulário Principal -->
                <div class="form-card">
                    <div class="form-header">
                        <h2 class="mb-0">
                            <i class="fas fa-clipboard-list me-3"></i>
                            Criar Novo Plano de Ação
                        </h2>
                        <p class="mb-0 mt-2 opacity-75">Preencha todos os campos para estruturar seu plano de ação</p>
                    </div>

                    <div class="form-body">
                        <form method="POST" id="formNovoPlano">

                            <!-- Seção: Identificação -->
                            <div class="form-section">
                                <h4 class="section-title">
                                    <i class="fas fa-tag"></i>
                                    Identificação do Plano
                                </h4>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="nome" class="form-label fw-bold">Nome do Plano *</label>
                                            <input type="text" class="form-control" id="nome" name="nome"
                                                   value="<?php echo htmlspecialchars($_POST['nome'] ?? ''); ?>"
                                                   placeholder="Ex: Implementação do Sistema de Gestão" required>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label for="status_id" class="form-label fw-bold">Status Inicial *</label>
                                            <select class="form-select" id="status_id" name="status_id" required>
                                                <option value="">Selecione o status</option>
                                                <?php foreach ($status_list as $status): ?>
                                                <option value="<?php echo $status['id']; ?>"
                                                        <?php echo (($_POST['status_id'] ?? '') == $status['id']) ? 'selected' : ''; ?>
                                                        data-cor="<?php echo $status['cor']; ?>"
                                                        data-icone="<?php echo $status['icone']; ?>">
                                                    <?php echo htmlspecialchars($status['nome']); ?>
                                                </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label for="legenda_id" class="form-label fw-bold">
                                                <i class="fas fa-bullseye me-1"></i>Objetivo Estratégico
                                            </label>
                                            <select class="form-select" id="legenda_id" name="legenda_id">
                                                <option value="">Selecione a legenda</option>
                                                <?php foreach ($legendas_list as $legenda): ?>
                                                <option value="<?php echo $legenda['id']; ?>"
                                                        <?php echo (($_POST['legenda_id'] ?? '') == $legenda['id']) ? 'selected' : ''; ?>
                                                        data-cor="<?php echo $legenda['cor']; ?>">
                                                    <?php echo htmlspecialchars($legenda['nome']); ?>
                                                </option>
                                                <?php endforeach; ?>
                                            </select>
                                            <div class="form-text">
                                                <i class="fas fa-info-circle me-1"></i>
                                                Conecte seu plano a um objetivo estratégico
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Seção: 5W2H -->
                            <div class="form-section">
                                <h4 class="section-title">
                                    <i class="fas fa-question-circle"></i>
                                    Metodologia 5W2H
                                </h4>

                                <!-- O QUE -->
                                <div class="mb-4">
                                    <label for="oque" class="form-label fw-bold">
                                        <i class="fas fa-bullseye text-primary me-2"></i>O QUE será feito? *
                                    </label>
                                    <textarea class="form-control" id="oque" name="oque" rows="3"
                                              placeholder="Descreva detalhadamente o que será realizado..." required><?php echo htmlspecialchars($_POST['oque'] ?? ''); ?></textarea>
                                </div>

                                <!-- POR QUE -->
                                <div class="mb-4">
                                    <label for="porque" class="form-label fw-bold">
                                        <i class="fas fa-lightbulb text-warning me-2"></i>POR QUE será feito? *
                                    </label>
                                    <textarea class="form-control" id="porque" name="porque" rows="3"
                                              placeholder="Justifique a necessidade e os objetivos..." required><?php echo htmlspecialchars($_POST['porque'] ?? ''); ?></textarea>
                                </div>

                                <!-- ONDE -->
                                <div class="mb-4">
                                    <label for="onde" class="form-label fw-bold">
                                        <i class="fas fa-map-marker-alt text-danger me-2"></i>ONDE será executado?
                                    </label>
                                    <input type="text" class="form-control" id="onde" name="onde"
                                           value="<?php echo htmlspecialchars($_POST['onde'] ?? ''); ?>"
                                           placeholder="Ex: Sede principal, Filial Centro, Remotamente...">
                                </div>

                                <!-- QUANDO -->
                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <label for="quando_inicio" class="form-label fw-bold">
                                            <i class="fas fa-calendar-alt text-success me-2"></i>QUANDO inicia? *
                                        </label>
                                        <input type="date" class="form-control" id="quando_inicio" name="quando_inicio"
                                               value="<?php echo $_POST['quando_inicio'] ?? ''; ?>" required>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="quando_fim" class="form-label fw-bold">
                                            <i class="fas fa-calendar-check text-success me-2"></i>QUANDO termina? *
                                        </label>
                                        <input type="date" class="form-control" id="quando_fim" name="quando_fim"
                                               value="<?php echo $_POST['quando_fim'] ?? ''; ?>" required>
                                    </div>
                                </div>

                                <!-- COMO -->
                                <div class="mb-4">
                                    <label for="como" class="form-label fw-bold">
                                        <i class="fas fa-cogs text-info me-2"></i>COMO será executado? *
                                    </label>
                                    <textarea class="form-control" id="como" name="como" rows="4"
                                              placeholder="Descreva a metodologia, etapas, recursos necessários..." required><?php echo htmlspecialchars($_POST['como'] ?? ''); ?></textarea>
                                </div>

                                <!-- QUANTO CUSTA -->
                                <div class="mb-4">
                                    <label for="quanto_custa" class="form-label fw-bold">
                                        <i class="fas fa-dollar-sign text-success me-2"></i>QUANTO custa?
                                    </label>
                                    <div class="input-group">
                                        <span class="input-group-text">R$</span>
                                        <input type="text" class="form-control" id="quanto_custa" name="quanto_custa"
                                               value="<?php echo $_POST['quanto_custa'] ?? ''; ?>"
                                               placeholder="0,00" data-mask="currency">
                                        <input type="hidden" id="quanto_custa_value" name="quanto_custa_value">
                                    </div>
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Digite apenas números. Ex: 1500,50
                                    </small>
                                </div>
                            </div>

                            <!-- Seção: Responsáveis -->
                            <div class="form-section">
                                <h4 class="section-title">
                                    <i class="fas fa-users"></i>
                                    Responsáveis (QUEM)
                                </h4>

                                <div class="mb-3">
                                    <label for="busca_responsavel" class="form-label fw-bold">Buscar e selecionar responsáveis:</label>
                                    <div class="input-group mb-3">
                                        <span class="input-group-text">
                                            <i class="fas fa-search"></i>
                                        </span>
                                        <input type="text" class="form-control" id="busca_responsavel"
                                               placeholder="Digite o nome do responsável ou setor...">
                                        <button type="button" class="btn btn-outline-secondary" onclick="limparBusca()">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>

                                    <!-- Lista de responsáveis selecionados -->
                                    <div id="responsaveis_selecionados" class="mb-3" style="display: none;">
                                        <label class="form-label fw-bold text-success">
                                            <i class="fas fa-check-circle me-2"></i>Responsáveis Selecionados:
                                        </label>
                                        <div id="lista_selecionados" class="d-flex flex-wrap gap-2"></div>
                                    </div>

                                    <!-- Grid de responsáveis com busca -->
                                    <div class="responsaveis-grid" id="responsaveis_grid">
                                        <?php foreach ($responsaveis_list as $responsavel): ?>
                                        <div class="responsavel-item" data-nome="<?php echo strtolower(htmlspecialchars($responsavel['nome'])); ?>"
                                             data-tipo="<?php echo $responsavel['tipo']; ?>">
                                            <input type="checkbox"
                                                   id="resp_<?php echo $responsavel['id']; ?>"
                                                   name="responsaveis[]"
                                                   value="<?php echo $responsavel['id']; ?>"
                                                   data-nome="<?php echo htmlspecialchars($responsavel['nome']); ?>"
                                                   data-tipo="<?php echo ucfirst($responsavel['tipo']); ?>"
                                                   onchange="atualizarSelecionados()"
                                                   <?php echo (in_array($responsavel['id'], $_POST['responsaveis'] ?? [])) ? 'checked' : ''; ?>>
                                            <label for="resp_<?php echo $responsavel['id']; ?>" class="mb-0 flex-grow-1">
                                                <?php echo htmlspecialchars($responsavel['nome']); ?>
                                            </label>
                                            <span class="responsavel-tipo <?php echo ($responsavel['tipo'] == 'usuario') ? 'tipo-usuario' : 'tipo-setor'; ?>">
                                                <?php echo ucfirst($responsavel['tipo']); ?>
                                            </span>
                                        </div>
                                        <?php endforeach; ?>
                                    </div>

                                    <small class="text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Use a busca para encontrar responsáveis rapidamente. Você pode selecionar múltiplos responsáveis.
                                    </small>
                                </div>
                            </div>

                            <!-- Seção: Resultados e Observações -->
                            <div class="form-section">
                                <h4 class="section-title">
                                    <i class="fas fa-chart-line"></i>
                                    Resultados e Observações
                                </h4>

                                <div class="mb-3">
                                    <label for="resultados" class="form-label fw-bold">Resultados Esperados</label>
                                    <textarea class="form-control" id="resultados" name="resultados" rows="4"
                                              placeholder="Descreva os resultados esperados, métricas de sucesso, indicadores..."><?php echo htmlspecialchars($_POST['resultados'] ?? ''); ?></textarea>
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Este campo será registrado como uma interação inicial do plano.
                                    </small>
                                </div>
                            </div>

                            <!-- Botões de Ação -->
                            <div class="d-flex justify-content-between align-items-center pt-4 border-top">
                                <a href="index.php" class="btn btn-outline-sicoob">
                                    <i class="fas fa-arrow-left me-2"></i>Cancelar
                                </a>

                                <div>
                                    <button type="button" class="btn btn-outline-sicoob me-2" onclick="limparFormulario()">
                                        <i class="fas fa-eraser me-2"></i>Limpar
                                    </button>
                                    <button type="submit" class="btn btn-sicoob">
                                        <i class="fas fa-save me-2"></i>Criar Plano de Ação
                                    </button>
                                </div>
                            </div>

                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Validação de datas e inicialização
        document.addEventListener('DOMContentLoaded', function() {
            const inicioInput = document.getElementById('quando_inicio');
            const fimInput = document.getElementById('quando_fim');

            function validarDatas() {
                if (inicioInput.value && fimInput.value) {
                    const inicio = new Date(inicioInput.value);
                    const fim = new Date(fimInput.value);

                    if (fim < inicio) {
                        fimInput.setCustomValidity('A data de fim deve ser posterior à data de início');
                    } else {
                        fimInput.setCustomValidity('');
                    }
                }
            }

            inicioInput.addEventListener('change', validarDatas);
            fimInput.addEventListener('change', validarDatas);

            // Definir data mínima como hoje
            const hoje = new Date().toISOString().split('T')[0];
            inicioInput.setAttribute('min', hoje);
            fimInput.setAttribute('min', hoje);

            // Inicializar máscara de moeda
            initCurrencyMask();

            // Inicializar busca de responsáveis
            initResponsaveisSearch();

            // Atualizar lista de selecionados na inicialização
            atualizarSelecionados();
        });

        // Máscara de moeda
        function initCurrencyMask() {
            const input = document.getElementById('quanto_custa');
            const hiddenInput = document.getElementById('quanto_custa_value');

            input.addEventListener('input', function(e) {
                let value = e.target.value.replace(/\D/g, '');

                if (value === '') {
                    e.target.value = '';
                    hiddenInput.value = '0';
                    return;
                }

                // Converter para formato de moeda
                value = (parseInt(value) / 100).toFixed(2);
                e.target.value = value.replace('.', ',').replace(/\B(?=(\d{3})+(?!\d))/g, '.');

                // Valor numérico para envio
                hiddenInput.value = value;
            });

            // Processar valor inicial se existir
            if (input.value) {
                input.dispatchEvent(new Event('input'));
            }
        }

        // Busca de responsáveis
        function initResponsaveisSearch() {
            const searchInput = document.getElementById('busca_responsavel');
            const grid = document.getElementById('responsaveis_grid');
            const items = grid.querySelectorAll('.responsavel-item');

            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase().trim();

                items.forEach(item => {
                    const nome = item.getAttribute('data-nome');
                    const tipo = item.getAttribute('data-tipo');

                    if (searchTerm === '' ||
                        nome.includes(searchTerm) ||
                        tipo.includes(searchTerm)) {
                        item.classList.remove('hidden');
                    } else {
                        item.classList.add('hidden');
                    }
                });
            });
        }

        // Limpar busca
        function limparBusca() {
            document.getElementById('busca_responsavel').value = '';
            const items = document.querySelectorAll('.responsavel-item');
            items.forEach(item => item.classList.remove('hidden'));
        }

        // Atualizar lista de responsáveis selecionados
        function atualizarSelecionados() {
            const checkboxes = document.querySelectorAll('input[name="responsaveis[]"]:checked');
            const container = document.getElementById('lista_selecionados');
            const sectionContainer = document.getElementById('responsaveis_selecionados');

            container.innerHTML = '';

            if (checkboxes.length > 0) {
                sectionContainer.style.display = 'block';

                checkboxes.forEach(checkbox => {
                    const nome = checkbox.getAttribute('data-nome');
                    const tipo = checkbox.getAttribute('data-tipo');

                    const badge = document.createElement('div');
                    badge.className = 'responsavel-selecionado';
                    badge.innerHTML = `
                        <span>${nome} <small>(${tipo})</small></span>
                        <button type="button" class="btn-remove" onclick="removerResponsavel('${checkbox.id}')">
                            <i class="fas fa-times"></i>
                        </button>
                    `;

                    container.appendChild(badge);
                });
            } else {
                sectionContainer.style.display = 'none';
            }
        }

        // Remover responsável selecionado
        function removerResponsavel(checkboxId) {
            const checkbox = document.getElementById(checkboxId);
            if (checkbox) {
                checkbox.checked = false;
                atualizarSelecionados();
            }
        }

        // Função para limpar formulário
        function limparFormulario() {
            if (confirm('Tem certeza que deseja limpar todos os campos do formulário?')) {
                document.getElementById('formNovoPlano').reset();

                // Limpar checkboxes e atualizar lista
                const checkboxes = document.querySelectorAll('input[name="responsaveis[]"]');
                checkboxes.forEach(cb => cb.checked = false);
                atualizarSelecionados();

                // Limpar busca
                limparBusca();

                // Limpar campo de moeda
                document.getElementById('quanto_custa').value = '';
                document.getElementById('quanto_custa_value').value = '0';
            }
        }

        // Preview do status selecionado
        document.getElementById('status_id').addEventListener('change', function() {
            const option = this.options[this.selectedIndex];
            if (option.value) {
                const cor = option.getAttribute('data-cor');
                const icone = option.getAttribute('data-icone');

                // Criar preview visual do status
                let preview = document.getElementById('status-preview');
                if (!preview) {
                    preview = document.createElement('div');
                    preview.id = 'status-preview';
                    preview.className = 'mt-2';
                    this.parentNode.appendChild(preview);
                }

                preview.innerHTML = `
                    <small class="d-flex align-items-center">
                        <i class="${icone} me-2" style="color: ${cor};"></i>
                        <span style="color: ${cor}; font-weight: 600;">${option.text}</span>
                    </small>
                `;
            }
        });

        // Contador de caracteres para textareas
        const textareas = document.querySelectorAll('textarea');
        textareas.forEach(textarea => {
            const maxLength = textarea.getAttribute('maxlength');
            if (maxLength) {
                const counter = document.createElement('small');
                counter.className = 'text-muted float-end';
                textarea.parentNode.appendChild(counter);

                function updateCounter() {
                    const remaining = maxLength - textarea.value.length;
                    counter.textContent = `${textarea.value.length}/${maxLength} caracteres`;
                    counter.style.color = remaining < 50 ? '#D32F2F' : '#6B7280';
                }

                textarea.addEventListener('input', updateCounter);
                updateCounter();
            }
        });

        // Validação do formulário antes do envio
        document.getElementById('formNovoPlano').addEventListener('submit', function(e) {
            const requiredFields = this.querySelectorAll('[required]');
            let isValid = true;

            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    field.classList.add('is-invalid');
                    isValid = false;
                } else {
                    field.classList.remove('is-invalid');
                }
            });

            if (!isValid) {
                e.preventDefault();
                alert('Por favor, preencha todos os campos obrigatórios.');
                return false;
            }

            // Confirmar criação
            if (!confirm('Confirma a criação deste plano de ação?')) {
                e.preventDefault();
                return false;
            }
        });
    </script>
</body>
</html>
