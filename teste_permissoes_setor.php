<?php
session_start();
require_once 'config/database.php';

// Simular usuário logado para teste
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1; // Assumindo que existe um usuário com ID 1
}

echo "<h2>🧪 Teste de Permissões por Setor - Planos de Ação</h2>";

try {
    // 1. Verificar dados do usuário
    echo "<h3>👤 Dados do Usuário</h3>";
    $stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $usuario = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$usuario) {
        echo "<div class='alert alert-danger'>❌ Usuário não encontrado!</div>";
        exit;
    }
    
    echo "<div class='user-info'>";
    echo "<p><strong>ID:</strong> {$usuario['id']}</p>";
    echo "<p><strong>Nome:</strong> {$usuario['nome_completo']}</p>";
    echo "<p><strong>Username:</strong> {$usuario['username']}</p>";
    echo "<p><strong>Nível de Acesso:</strong> {$usuario['nivel_acesso_id']}</p>";
    echo "<p><strong>É Admin:</strong> " . (in_array($usuario['nivel_acesso_id'], [1, 2]) ? 'Sim' : 'Não') . "</p>";
    echo "</div>";
    
    // 2. Verificar setores do usuário
    echo "<h3>🏢 Setores do Usuário</h3>";
    $stmt = $pdo->prepare("
        SELECT s.id, s.nome
        FROM setores s
        INNER JOIN usuario_setor us ON s.id = us.setor_id
        WHERE us.usuario_id = ?
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $setores = $stmt->fetchAll();
    
    if (empty($setores)) {
        echo "<div class='alert alert-warning'>⚠️ Usuário não está associado a nenhum setor</div>";
    } else {
        echo "<div class='alert alert-info'>📊 Usuário está associado a " . count($setores) . " setor(es)</div>";
        echo "<ul>";
        foreach ($setores as $setor) {
            echo "<li><strong>{$setor['nome']}</strong> (ID: {$setor['id']})</li>";
        }
        echo "</ul>";
    }
    
    // 3. Verificar permissões diretas do usuário
    echo "<h3>🔑 Permissões Diretas do Usuário</h3>";
    $stmt = $pdo->prepare("
        SELECT cb.id, cb.nome, cb.link, cb.ativo
        FROM card_button_usuarios cbu
        INNER JOIN card_buttons cb ON cbu.button_id = cb.id
        WHERE cbu.usuario_id = ?
        ORDER BY cb.nome
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $permissoes_diretas = $stmt->fetchAll();
    
    if (empty($permissoes_diretas)) {
        echo "<div class='alert alert-warning'>⚠️ Usuário não tem permissões diretas</div>";
    } else {
        echo "<div class='alert alert-success'>✅ Usuário tem " . count($permissoes_diretas) . " permissão(ões) direta(s)</div>";
        echo "<table class='table table-striped'>";
        echo "<tr><th>ID</th><th>Nome</th><th>Link</th><th>Ativo</th></tr>";
        foreach ($permissoes_diretas as $perm) {
            $ativo_badge = $perm['ativo'] ? '<span class="badge bg-success">Ativo</span>' : '<span class="badge bg-danger">Inativo</span>';
            echo "<tr>";
            echo "<td>{$perm['id']}</td>";
            echo "<td>{$perm['nome']}</td>";
            echo "<td>{$perm['link']}</td>";
            echo "<td>{$ativo_badge}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 4. Verificar permissões por setor
    echo "<h3>🏢 Permissões por Setor</h3>";
    $stmt = $pdo->prepare("
        SELECT cb.id, cb.nome, cb.link, cb.ativo, s.nome as setor_nome
        FROM card_button_setores cbs
        INNER JOIN card_buttons cb ON cbs.button_id = cb.id
        INNER JOIN usuario_setor us ON cbs.setor_id = us.setor_id
        INNER JOIN setores s ON us.setor_id = s.id
        WHERE us.usuario_id = ?
        ORDER BY s.nome, cb.nome
    ");
    $stmt->execute([$_SESSION['user_id']]);
    $permissoes_setor = $stmt->fetchAll();
    
    if (empty($permissoes_setor)) {
        echo "<div class='alert alert-warning'>⚠️ Usuário não tem permissões por setor</div>";
    } else {
        echo "<div class='alert alert-success'>✅ Usuário tem " . count($permissoes_setor) . " permissão(ões) por setor</div>";
        echo "<table class='table table-striped'>";
        echo "<tr><th>Setor</th><th>Botão ID</th><th>Nome</th><th>Link</th><th>Ativo</th></tr>";
        foreach ($permissoes_setor as $perm) {
            $ativo_badge = $perm['ativo'] ? '<span class="badge bg-success">Ativo</span>' : '<span class="badge bg-danger">Inativo</span>';
            echo "<tr>";
            echo "<td><strong>{$perm['setor_nome']}</strong></td>";
            echo "<td>{$perm['id']}</td>";
            echo "<td>{$perm['nome']}</td>";
            echo "<td>{$perm['link']}</td>";
            echo "<td>{$ativo_badge}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 5. Teste da lógica de permissões dos planos
    echo "<h3>🎯 Teste da Lógica de Permissões dos Planos</h3>";
    
    $is_admin = in_array($usuario['nivel_acesso_id'], [1, 2]);
    
    if ($is_admin) {
        echo "<div class='alert alert-success'>✅ <strong>ACESSO LIBERADO</strong> - Usuário é administrador</div>";
        $tem_permissao = true;
    } else {
        echo "<div class='alert alert-info'>ℹ️ Usuário não é administrador, verificando permissões...</div>";
        
        // Verificar permissão direta
        $stmt = $pdo->prepare("
            SELECT COUNT(*)
            FROM card_button_usuarios cbu
            WHERE cbu.usuario_id = ?
        ");
        $stmt->execute([$_SESSION['user_id']]);
        $tem_permissao_usuario = $stmt->fetchColumn() > 0;
        
        if ($tem_permissao_usuario) {
            echo "<div class='alert alert-success'>✅ <strong>ACESSO LIBERADO</strong> - Usuário tem permissão direta</div>";
            $tem_permissao = true;
        } else {
            echo "<div class='alert alert-warning'>⚠️ Usuário não tem permissão direta, verificando por setor...</div>";
            
            // Verificar permissão por setor
            $stmt = $pdo->prepare("
                SELECT COUNT(*)
                FROM card_button_setores cbs
                INNER JOIN usuario_setor us ON cbs.setor_id = us.setor_id
                WHERE us.usuario_id = ?
            ");
            $stmt->execute([$_SESSION['user_id']]);
            $tem_permissao_setor = $stmt->fetchColumn() > 0;
            
            if ($tem_permissao_setor) {
                echo "<div class='alert alert-success'>✅ <strong>ACESSO LIBERADO</strong> - Usuário tem permissão por setor</div>";
                $tem_permissao = true;
            } else {
                echo "<div class='alert alert-danger'>❌ <strong>ACESSO NEGADO</strong> - Usuário não tem permissões</div>";
                $tem_permissao = false;
            }
        }
    }
    
    // 6. Resultado final
    echo "<h3>🎯 Resultado Final</h3>";
    if ($tem_permissao) {
        echo "<div class='alert alert-success result-box'>";
        echo "<h4>✅ ACESSO AUTORIZADO</h4>";
        echo "<p>O usuário <strong>{$usuario['nome_completo']}</strong> tem acesso aos Planos de Ação.</p>";
        echo "</div>";
    } else {
        echo "<div class='alert alert-danger result-box'>";
        echo "<h4>❌ ACESSO NEGADO</h4>";
        echo "<p>O usuário <strong>{$usuario['nome_completo']}</strong> NÃO tem acesso aos Planos de Ação.</p>";
        echo "</div>";
    }
    
    // 7. Links para teste
    echo "<h3>🔗 Links para Teste</h3>";
    echo "<div class='test-links'>";
    echo "<a href='planos/index.php' class='btn btn-primary'>📋 Acessar Planos de Ação</a> ";
    echo "<a href='planos/novo.php' class='btn btn-success'>🆕 Criar Novo Plano</a> ";
    echo "<a href='planos/admin_status.php' class='btn btn-warning'>⚙️ Admin Status</a> ";
    echo "<a href='planos/admin_legendas.php' class='btn btn-info'>🎯 Admin Legendas</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ Erro: " . $e->getMessage() . "</div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background: #f8f9fa;
    line-height: 1.6;
}

h2, h3 {
    color: #003641;
    margin-bottom: 15px;
}

.alert {
    padding: 15px;
    margin: 15px 0;
    border-radius: 8px;
    border: 1px solid transparent;
}

.alert-success {
    background: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.alert-warning {
    background: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.alert-info {
    background: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

.alert-danger {
    background: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.user-info {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin: 15px 0;
}

.table {
    width: 100%;
    border-collapse: collapse;
    margin: 15px 0;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.table th, .table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.table th {
    background: #003641;
    color: white;
    font-weight: 600;
}

.table-striped tr:nth-child(even) {
    background: #f8f9fa;
}

.badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.bg-success {
    background-color: #28a745;
    color: white;
}

.bg-danger {
    background-color: #dc3545;
    color: white;
}

.result-box {
    border: 3px solid;
    font-size: 1.1em;
}

.result-box h4 {
    margin-bottom: 10px;
    font-size: 1.3em;
}

.test-links {
    margin: 20px 0;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    margin: 5px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: linear-gradient(135deg, #00A091 0%, #008a7c 100%);
    color: white;
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    color: #212529;
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    text-decoration: none;
}

ul {
    margin: 10px 0;
    padding-left: 20px;
}

li {
    margin: 5px 0;
}
</style>
