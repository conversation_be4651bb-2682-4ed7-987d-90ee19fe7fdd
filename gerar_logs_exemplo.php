<?php
session_start();
require_once 'config/database.php';
require_once 'planos/PlanosLogger.php';

// Simular usuário logado para teste
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1; // Assumindo que existe um usuário com ID 1
}

echo "<h2>🧪 Gerando Logs de Exemplo para Planos</h2>";

try {
    $logger = new PlanosLogger($pdo);
    
    // 1. Log de criação de plano
    echo "<h3>📝 Criando Log de Criação de Plano</h3>";
    $dados_plano_exemplo = [
        'plano_id' => 123,
        'nome' => 'Implementação do Sistema de Gestão Integrada',
        'status_id' => 1,
        'legenda_id' => 2,
        'quando_inicio' => '2025-02-01',
        'quando_fim' => '2025-06-30',
        'quanto_custa' => 15000.50
    ];
    
    $resultado1 = $logger->logCriarPlano($_SESSION['user_id'], 123, $dados_plano_exemplo);
    echo $resultado1 ? "✅ Log de criação registrado<br>" : "❌ Falha no log de criação<br>";
    
    // 2. Log de edição de plano
    echo "<h3>✏️ Criando Log de Edição de Plano</h3>";
    $dados_anteriores = [
        'nome' => 'Sistema de Gestão Integrada',
        'quando_fim' => '2025-05-30',
        'quanto_custa' => 12000.00
    ];
    
    $dados_novos = [
        'nome' => 'Implementação do Sistema de Gestão Integrada',
        'quando_fim' => '2025-06-30',
        'quanto_custa' => 15000.50
    ];
    
    $resultado2 = $logger->logEditarPlano($_SESSION['user_id'], 123, $dados_anteriores, $dados_novos);
    echo $resultado2 ? "✅ Log de edição registrado<br>" : "❌ Falha no log de edição<br>";
    
    // 3. Log de mudança de status
    echo "<h3>🔄 Criando Log de Mudança de Status</h3>";
    $resultado3 = $logger->logMudarStatus($_SESSION['user_id'], 123, 1, 2, 'Plano aprovado pela diretoria e iniciado');
    echo $resultado3 ? "✅ Log de mudança de status registrado<br>" : "❌ Falha no log de mudança de status<br>";
    
    // 4. Log de adição de responsável
    echo "<h3>👥 Criando Log de Adição de Responsável</h3>";
    $responsavel_dados = [
        'usuario_id' => 5,
        'nome' => 'João Silva',
        'papel' => 'Coordenador do Projeto'
    ];
    $resultado4 = $logger->logAdicionarResponsavel($_SESSION['user_id'], 123, $responsavel_dados);
    echo $resultado4 ? "✅ Log de adição de responsável registrado<br>" : "❌ Falha no log de responsável<br>";
    
    // 5. Log de interação
    echo "<h3>💬 Criando Log de Interação</h3>";
    $resultado5 = $logger->logAdicionarInteracao($_SESSION['user_id'], 123, 'comentario', 'Reunião de Kickoff', 'Realizada reunião inicial com toda a equipe para alinhamento dos objetivos e cronograma do projeto.');
    echo $resultado5 ? "✅ Log de interação registrado<br>" : "❌ Falha no log de interação<br>";
    
    // 6. Log administrativo - criar status
    echo "<h3>⚙️ Criando Log Administrativo - Status</h3>";
    $admin_dados_status = [
        'status_id' => 10,
        'nome' => 'Em Revisão',
        'cor' => '#FFA500',
        'icone' => 'fas fa-search'
    ];
    $resultado6 = $logger->logAcaoAdmin($_SESSION['user_id'], 'criar_status', $admin_dados_status);
    echo $resultado6 ? "✅ Log administrativo de status registrado<br>" : "❌ Falha no log admin de status<br>";
    
    // 7. Log administrativo - criar legenda
    echo "<h3>🎯 Criando Log Administrativo - Legenda</h3>";
    $admin_dados_legenda = [
        'legenda_id' => 15,
        'nome' => 'Inovação Tecnológica',
        'cor' => '#9C27B0',
        'descricao' => 'Projetos focados em inovação e desenvolvimento tecnológico'
    ];
    $resultado7 = $logger->logAcaoAdmin($_SESSION['user_id'], 'criar_legenda', $admin_dados_legenda);
    echo $resultado7 ? "✅ Log administrativo de legenda registrado<br>" : "❌ Falha no log admin de legenda<br>";
    
    // 8. Segundo plano para variedade
    echo "<h3>📋 Criando Segundo Plano de Exemplo</h3>";
    $dados_plano_2 = [
        'plano_id' => 124,
        'nome' => 'Treinamento da Equipe de Atendimento',
        'status_id' => 2,
        'legenda_id' => 4,
        'quando_inicio' => '2025-03-01',
        'quando_fim' => '2025-04-15',
        'quanto_custa' => 5000.00
    ];
    
    $resultado8 = $logger->logCriarPlano($_SESSION['user_id'], 124, $dados_plano_2);
    echo $resultado8 ? "✅ Segundo plano registrado<br>" : "❌ Falha no segundo plano<br>";
    
    // 9. Mudança de status do segundo plano
    $resultado9 = $logger->logMudarStatus($_SESSION['user_id'], 124, 2, 3, 'Treinamento concluído com sucesso');
    echo $resultado9 ? "✅ Status do segundo plano alterado<br>" : "❌ Falha na alteração de status<br>";
    
    echo "<br><h3>📊 Resumo dos Logs Gerados</h3>";
    
    // Contar logs criados
    $stmt = $pdo->query("
        SELECT COUNT(*) as total 
        FROM logs 
        WHERE (acao LIKE '%plano%' OR acao LIKE 'admin_criar_status' OR acao LIKE 'admin_criar_legenda')
        AND data_hora >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
    ");
    $total_recentes = $stmt->fetch()['total'];
    
    echo "<div class='alert alert-success'>";
    echo "✅ <strong>{$total_recentes}</strong> logs relacionados aos planos foram criados na última hora.";
    echo "</div>";
    
    echo "<div class='test-links'>";
    echo "<a href='planos/logs.php' class='btn btn-primary'>📊 Ver Logs dos Planos</a> ";
    echo "<a href='planos/index.php' class='btn btn-secondary'>📋 Voltar aos Planos</a> ";
    echo "<a href='teste_logs_planos.php' class='btn btn-info'>🧪 Teste Completo</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ Erro: " . $e->getMessage() . "</div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background: #f8f9fa;
    line-height: 1.6;
}

h2, h3 {
    color: #003641;
    margin-bottom: 15px;
}

.alert {
    padding: 15px;
    margin: 15px 0;
    border-radius: 8px;
    border: 1px solid transparent;
}

.alert-success {
    background: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.alert-danger {
    background: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.test-links {
    margin: 20px 0;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    margin: 5px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: linear-gradient(135deg, #00A091 0%, #008a7c 100%);
    color: white;
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    color: white;
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    text-decoration: none;
}

pre {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
    overflow-x: auto;
    font-size: 0.8rem;
}
</style>
