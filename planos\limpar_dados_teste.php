<?php
require_once '../auth_check.php';
require_once '../config/database.php';
require_once 'PlanosLogger.php';

// Verificar permissões de acesso (apenas administradores)
$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ?");
$stmt->execute([$_SESSION['user_id']]);
$usuario = $stmt->fetch(PDO::FETCH_ASSOC);

$is_admin = in_array($usuario['nivel_acesso_id'], [1, 2]);

if (!$is_admin) {
    header('Location: index.php?error=acesso_negado');
    exit;
}

$erro = '';
$sucesso = '';
$confirmacao_requerida = false;

// Processar ações
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['acao'])) {
            switch ($_POST['acao']) {
                case 'confirmar_exclusao':
                    $confirmacao_requerida = true;
                    break;
                    
                case 'executar_limpeza':
                    if (isset($_POST['confirmar']) && $_POST['confirmar'] === 'SIM_EXCLUIR_TUDO') {
                        // Iniciar transação
                        $pdo->beginTransaction();
                        
                        // Contar registros antes da exclusão
                        $stmt = $pdo->query("SELECT COUNT(*) FROM planos");
                        $total_planos = $stmt->fetchColumn();

                        $stmt = $pdo->query("SELECT COUNT(*) FROM planos_responsaveis");
                        $total_responsaveis = $stmt->fetchColumn();

                        $stmt = $pdo->query("SELECT COUNT(*) FROM planos_historico_status");
                        $total_historico = $stmt->fetchColumn();

                        $stmt = $pdo->query("SELECT COUNT(*) FROM planos_interacoes");
                        $total_interacoes = $stmt->fetchColumn();

                        // Verificar se existe tabela de vínculos
                        $stmt = $pdo->query("SHOW TABLES LIKE 'planos_responsaveis_vinculo'");
                        $tabela_vinculo_existe = $stmt->fetch();

                        $total_vinculos = 0;
                        if ($tabela_vinculo_existe) {
                            $stmt = $pdo->query("SELECT COUNT(*) FROM planos_responsaveis_vinculo");
                            $total_vinculos = $stmt->fetchColumn();
                        }
                        
                        // Registrar log antes da exclusão
                        $logger = new PlanosLogger($pdo);
                        $detalhes_limpeza = [
                            'total_planos' => $total_planos,
                            'total_responsaveis' => $total_responsaveis,
                            'total_historico' => $total_historico,
                            'total_interacoes' => $total_interacoes,
                            'total_vinculos' => $total_vinculos,
                            'usuario_responsavel' => $usuario['nome_completo'],
                            'timestamp' => date('Y-m-d H:i:s')
                        ];
                        $logger->logAcaoAdmin($_SESSION['user_id'], 'limpeza_dados_teste', $detalhes_limpeza);
                        
                        // Excluir dados relacionados primeiro (devido às foreign keys)
                        // Ordem: das tabelas filhas para as tabelas pais

                        // 1. Excluir vínculos de responsáveis (se existir)
                        if ($tabela_vinculo_existe) {
                            $pdo->exec("DELETE FROM planos_responsaveis_vinculo");
                        }

                        // 2. Excluir interações dos planos
                        $pdo->exec("DELETE FROM planos_interacoes");

                        // 3. Excluir histórico de status
                        $pdo->exec("DELETE FROM planos_historico_status");

                        // 4. Excluir responsáveis dos planos
                        $pdo->exec("DELETE FROM planos_responsaveis");

                        // 5. Excluir planos (tabela principal)
                        $pdo->exec("DELETE FROM planos");

                        // Resetar auto_increment para começar do 1 novamente
                        $pdo->exec("ALTER TABLE planos AUTO_INCREMENT = 1");
                        $pdo->exec("ALTER TABLE planos_responsaveis AUTO_INCREMENT = 1");
                        $pdo->exec("ALTER TABLE planos_historico_status AUTO_INCREMENT = 1");
                        $pdo->exec("ALTER TABLE planos_interacoes AUTO_INCREMENT = 1");

                        // Resetar tabela de vínculos se existir
                        if ($tabela_vinculo_existe) {
                            $pdo->exec("ALTER TABLE planos_responsaveis_vinculo AUTO_INCREMENT = 1");
                        }
                        
                        // Confirmar transação
                        $pdo->commit();
                        
                        $mensagem_vinculos = $total_vinculos > 0 ? ", {$total_vinculos} vínculos" : "";
                        $sucesso = "Limpeza realizada com sucesso! Foram removidos: {$total_planos} planos, {$total_responsaveis} responsáveis, {$total_historico} registros de histórico, {$total_interacoes} interações{$mensagem_vinculos}.";
                        
                    } else {
                        $erro = 'Confirmação inválida. Digite exatamente "SIM_EXCLUIR_TUDO" para confirmar.';
                        $confirmacao_requerida = true;
                    }
                    break;
            }
        }
    } catch (Exception $e) {
        $pdo->rollBack();
        $erro = 'Erro ao executar limpeza: ' . $e->getMessage();
    }
}

// Buscar estatísticas atuais
try {
    $stmt = $pdo->query("SELECT COUNT(*) FROM planos");
    $total_planos = $stmt->fetchColumn();

    $stmt = $pdo->query("SELECT COUNT(*) FROM planos_responsaveis");
    $total_responsaveis = $stmt->fetchColumn();

    $stmt = $pdo->query("SELECT COUNT(*) FROM planos_historico_status");
    $total_historico = $stmt->fetchColumn();

    $stmt = $pdo->query("SELECT COUNT(*) FROM planos_interacoes");
    $total_interacoes = $stmt->fetchColumn();

    // Verificar se existe tabela de vínculos
    $stmt = $pdo->query("SHOW TABLES LIKE 'planos_responsaveis_vinculo'");
    $tabela_vinculo_existe = $stmt->fetch();

    $total_vinculos = 0;
    if ($tabela_vinculo_existe) {
        $stmt = $pdo->query("SELECT COUNT(*) FROM planos_responsaveis_vinculo");
        $total_vinculos = $stmt->fetchColumn();
    }
    
    // Buscar últimos planos criados
    $stmt = $pdo->query("
        SELECT p.id, p.nome, p.created_at, u.nome_completo as criado_por
        FROM planos p
        LEFT JOIN usuarios u ON p.criado_por = u.id
        ORDER BY p.created_at DESC
        LIMIT 10
    ");
    $ultimos_planos = $stmt->fetchAll();
    
} catch (Exception $e) {
    $total_planos = 0;
    $total_responsaveis = 0;
    $total_historico = 0;
    $total_interacoes = 0;
    $total_vinculos = 0;
    $tabela_vinculo_existe = false;
    $ultimos_planos = [];
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Limpar Dados de Teste - Sistema Sicoob</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- CSS Personalizado -->
    <link rel="stylesheet" href="../assets/css/sicoob-style.css">
    
    <style>
        .admin-header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        
        .warning-section {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .stats-section {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .confirmation-section {
            background: #f8d7da;
            border: 2px solid #dc3545;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .btn-danger-custom {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            border: none;
            color: white;
            font-weight: 600;
            padding: 12px 30px;
        }
        
        .btn-danger-custom:hover {
            background: linear-gradient(135deg, #c82333 0%, #a71e2a 100%);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4);
        }
        
        .stat-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            padding: 1.5rem;
            text-align: center;
            margin-bottom: 1rem;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #dc3545;
        }
        
        .stat-label {
            color: #6c757d;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="admin-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-0">
                        <i class="fas fa-trash-alt me-3"></i>
                        Limpar Dados de Teste
                    </h1>
                    <p class="mb-0 mt-2 opacity-75">⚠️ ATENÇÃO: Esta ação remove TODOS os planos e dados relacionados</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="index.php" class="btn btn-light">
                        <i class="fas fa-arrow-left me-2"></i>Voltar aos Planos
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Alertas -->
        <?php if ($erro): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo htmlspecialchars($erro); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if ($sucesso): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo htmlspecialchars($sucesso); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Aviso Importante -->
        <div class="warning-section">
            <h3 class="text-warning mb-3">
                <i class="fas fa-exclamation-triangle me-2"></i>
                ATENÇÃO - AÇÃO IRREVERSÍVEL
            </h3>
            <div class="row">
                <div class="col-md-8">
                    <p class="mb-2"><strong>Esta funcionalidade irá:</strong></p>
                    <ul class="mb-3">
                        <li>🗑️ Excluir TODOS os planos de ação</li>
                        <li>👥 Remover todos os responsáveis</li>
                        <?php if ($tabela_vinculo_existe): ?>
                        <li>🔗 Deletar todos os vínculos de responsáveis</li>
                        <?php endif; ?>
                        <li>📈 Apagar todo o histórico de status</li>
                        <li>💬 Deletar todas as interações/comentários</li>
                        <li>🔄 Resetar contadores para começar do ID 1</li>
                    </ul>
                    <p class="text-danger"><strong>⚠️ ESTA AÇÃO NÃO PODE SER DESFEITA!</strong></p>
                </div>
                <div class="col-md-4 text-center">
                    <i class="fas fa-exclamation-triangle text-warning" style="font-size: 5rem;"></i>
                </div>
            </div>
        </div>

        <!-- Estatísticas Atuais -->
        <div class="stats-section">
            <h3 class="mb-4">
                <i class="fas fa-chart-bar me-2"></i>
                Dados Atuais no Sistema
            </h3>
            
            <div class="row">
                <?php if ($tabela_vinculo_existe): ?>
                <!-- Com vínculos - 5 colunas -->
                <div class="col-md-2">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo number_format($total_planos); ?></div>
                        <div class="stat-label">Planos</div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo number_format($total_responsaveis); ?></div>
                        <div class="stat-label">Responsáveis</div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo number_format($total_vinculos); ?></div>
                        <div class="stat-label">Vínculos</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo number_format($total_historico); ?></div>
                        <div class="stat-label">Histórico</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo number_format($total_interacoes); ?></div>
                        <div class="stat-label">Interações</div>
                    </div>
                </div>
                <?php else: ?>
                <!-- Sem vínculos - 4 colunas -->
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo number_format($total_planos); ?></div>
                        <div class="stat-label">Planos</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo number_format($total_responsaveis); ?></div>
                        <div class="stat-label">Responsáveis</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo number_format($total_historico); ?></div>
                        <div class="stat-label">Histórico</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo number_format($total_interacoes); ?></div>
                        <div class="stat-label">Interações</div>
                    </div>
                </div>
                <?php endif; ?>
            </div>

            <?php if (!empty($ultimos_planos)): ?>
            <h4 class="mt-4 mb-3">Últimos Planos Criados:</h4>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead class="table-dark">
                        <tr>
                            <th>ID</th>
                            <th>Nome</th>
                            <th>Criado por</th>
                            <th>Data de Criação</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($ultimos_planos as $plano): ?>
                        <tr>
                            <td><?php echo $plano['id']; ?></td>
                            <td><?php echo htmlspecialchars($plano['nome']); ?></td>
                            <td><?php echo htmlspecialchars($plano['criado_por'] ?: 'N/A'); ?></td>
                            <td><?php echo date('d/m/Y H:i', strtotime($plano['created_at'])); ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php endif; ?>
        </div>

        <!-- Formulário de Confirmação -->
        <?php if ($confirmacao_requerida): ?>
        <div class="confirmation-section">
            <h3 class="text-danger mb-3">
                <i class="fas fa-skull-crossbones me-2"></i>
                CONFIRMAÇÃO FINAL
            </h3>
            
            <form method="POST">
                <input type="hidden" name="acao" value="executar_limpeza">
                
                <p class="mb-3"><strong>Para confirmar a exclusão de TODOS os dados, digite exatamente:</strong></p>
                <p class="text-danger fs-5 fw-bold mb-3">SIM_EXCLUIR_TUDO</p>
                
                <div class="mb-3">
                    <input type="text" class="form-control form-control-lg" name="confirmar" 
                           placeholder="Digite: SIM_EXCLUIR_TUDO" required>
                </div>
                
                <div class="d-flex gap-3">
                    <button type="submit" class="btn btn-danger-custom btn-lg">
                        <i class="fas fa-trash-alt me-2"></i>
                        EXECUTAR LIMPEZA COMPLETA
                    </button>
                    
                    <a href="limpar_dados_teste.php" class="btn btn-secondary btn-lg">
                        <i class="fas fa-times me-2"></i>Cancelar
                    </a>
                </div>
            </form>
        </div>
        <?php else: ?>
        
        <!-- Botão Inicial -->
        <?php if ($total_planos > 0): ?>
        <div class="text-center">
            <form method="POST">
                <input type="hidden" name="acao" value="confirmar_exclusao">
                <button type="submit" class="btn btn-danger-custom btn-lg">
                    <i class="fas fa-trash-alt me-2"></i>
                    INICIAR LIMPEZA DOS DADOS
                </button>
            </form>
            <p class="text-muted mt-3">
                <i class="fas fa-info-circle me-1"></i>
                Clique para prosseguir com a confirmação
            </p>
        </div>
        <?php else: ?>
        <div class="text-center">
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                <strong>Sistema já está limpo!</strong> Não há dados para remover.
            </div>
            <a href="index.php" class="btn btn-primary">
                <i class="fas fa-arrow-left me-2"></i>Voltar aos Planos
            </a>
        </div>
        <?php endif; ?>
        
        <?php endif; ?>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
