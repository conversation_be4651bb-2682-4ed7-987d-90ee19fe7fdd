<?php
require_once '../auth_check.php';
require_once '../config/database.php';
require_once 'PlanosLogger.php';

// Verificar permissões de acesso
$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ?");
$stmt->execute([$_SESSION['user_id']]);
$usuario = $stmt->fetch(PDO::FETCH_ASSOC);

$is_admin = in_array($usuario['nivel_acesso_id'], [1, 2]);

if ($is_admin) {
    $tem_permissao = true;
} else {
    // Verificar permissões de acesso aos planos (usuário direto ou por setor)
    $tem_permissao = false;

    try {
        // 1. Verificar se tem acesso direto por usuário a qualquer botão
        $stmt = $pdo->prepare("
            SELECT COUNT(*)
            FROM card_button_usuarios cbu
            WHERE cbu.usuario_id = ?
        ");
        $stmt->execute([$_SESSION['user_id']]);
        $tem_permissao_usuario = $stmt->fetchColumn() > 0;

        if ($tem_permissao_usuario) {
            $tem_permissao = true;
        } else {
            // 2. Verificar se tem acesso por setor a qualquer botão
            $stmt = $pdo->prepare("
                SELECT COUNT(*)
                FROM card_button_setores cbs
                INNER JOIN usuario_setor us ON cbs.setor_id = us.setor_id
                WHERE us.usuario_id = ?
            ");
            $stmt->execute([$_SESSION['user_id']]);
            $tem_permissao_setor = $stmt->fetchColumn() > 0;

            if ($tem_permissao_setor) {
                $tem_permissao = true;
            }
        }

    } catch (Exception $e) {
        // Se der erro, permitir acesso para usuários logados
        $tem_permissao = true;
    }
}

if (!$tem_permissao) {
    header('Location: index.php?error=acesso_negado');
    exit;
}

$erro = '';
$sucesso = '';

// Processar ações
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $logger = new PlanosLogger($pdo);

        if (isset($_POST['acao'])) {
            switch ($_POST['acao']) {
                case 'criar':
                    $stmt = $pdo->prepare("INSERT INTO planos_status (nome, cor, icone, descricao, ordem) VALUES (?, ?, ?, ?, ?)");
                    $stmt->execute([
                        $_POST['nome'],
                        $_POST['cor'],
                        $_POST['icone'],
                        $_POST['descricao'],
                        $_POST['ordem']
                    ]);
                    $status_id = $pdo->lastInsertId();

                    // Log da criação
                    $logger->logAcaoAdmin($_SESSION['user_id'], 'criar_status', [
                        'status_id' => $status_id,
                        'nome' => $_POST['nome'],
                        'cor' => $_POST['cor'],
                        'icone' => $_POST['icone']
                    ]);

                    $sucesso = 'Status criado com sucesso!';
                    break;
                    
                case 'editar':
                    // Buscar dados anteriores
                    $stmt_anterior = $pdo->prepare("SELECT * FROM planos_status WHERE id = ?");
                    $stmt_anterior->execute([$_POST['id']]);
                    $dados_anteriores = $stmt_anterior->fetch(PDO::FETCH_ASSOC);

                    $stmt = $pdo->prepare("UPDATE planos_status SET nome = ?, cor = ?, icone = ?, descricao = ?, ordem = ? WHERE id = ?");
                    $stmt->execute([
                        $_POST['nome'],
                        $_POST['cor'],
                        $_POST['icone'],
                        $_POST['descricao'],
                        $_POST['ordem'],
                        $_POST['id']
                    ]);

                    // Log da edição
                    $logger->logAcaoAdmin($_SESSION['user_id'], 'editar_status', [
                        'status_id' => $_POST['id'],
                        'dados_anteriores' => $dados_anteriores,
                        'dados_novos' => [
                            'nome' => $_POST['nome'],
                            'cor' => $_POST['cor'],
                            'icone' => $_POST['icone'],
                            'descricao' => $_POST['descricao'],
                            'ordem' => $_POST['ordem']
                        ]
                    ]);

                    $sucesso = 'Status atualizado com sucesso!';
                    break;
                    
                case 'excluir':
                    // Verificar se o status está sendo usado
                    $stmt = $pdo->prepare("SELECT COUNT(*) FROM planos WHERE status_id = ?");
                    $stmt->execute([$_POST['id']]);
                    $em_uso = $stmt->fetchColumn();

                    if ($em_uso > 0) {
                        $erro = "Não é possível excluir este status pois está sendo usado por {$em_uso} plano(s).";
                    } else {
                        // Buscar dados antes de excluir
                        $stmt_dados = $pdo->prepare("SELECT * FROM planos_status WHERE id = ?");
                        $stmt_dados->execute([$_POST['id']]);
                        $dados_status = $stmt_dados->fetch(PDO::FETCH_ASSOC);

                        $stmt = $pdo->prepare("DELETE FROM planos_status WHERE id = ?");
                        $stmt->execute([$_POST['id']]);

                        // Log da exclusão
                        $logger->logAcaoAdmin($_SESSION['user_id'], 'excluir_status', [
                            'status_id' => $_POST['id'],
                            'dados_excluidos' => $dados_status
                        ]);

                        $sucesso = 'Status excluído com sucesso!';
                    }
                    break;

                case 'toggle_ativo':
                    // Buscar estado atual
                    $stmt_atual = $pdo->prepare("SELECT ativo FROM planos_status WHERE id = ?");
                    $stmt_atual->execute([$_POST['id']]);
                    $ativo_atual = $stmt_atual->fetchColumn();

                    $stmt = $pdo->prepare("UPDATE planos_status SET ativo = NOT ativo WHERE id = ?");
                    $stmt->execute([$_POST['id']]);

                    // Log da mudança
                    $logger->logAcaoAdmin($_SESSION['user_id'], 'toggle_status_ativo', [
                        'status_id' => $_POST['id'],
                        'ativo_anterior' => $ativo_atual,
                        'ativo_novo' => !$ativo_atual
                    ]);

                    $sucesso = 'Status atualizado com sucesso!';
                    break;
            }
        }
    } catch (Exception $e) {
        $erro = 'Erro: ' . $e->getMessage();
    }
}

// Buscar todos os status
$stmt = $pdo->query("SELECT * FROM planos_status ORDER BY ordem, nome");
$status_list = $stmt->fetchAll();

// Buscar status para edição
$status_edicao = null;
if (isset($_GET['editar'])) {
    $stmt = $pdo->prepare("SELECT * FROM planos_status WHERE id = ?");
    $stmt->execute([$_GET['editar']]);
    $status_edicao = $stmt->fetch();
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gerenciar Status - Sistema Sicoob</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="../assets/images/Sicoob.ico">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            /* Paleta de Cores Oficial Sicoob 2024 - Manual de Marca */
            --sicoob-turquesa: #00A091;        /* RGB: 0, 160, 145 - Cor principal */
            --sicoob-verde-escuro: #003641;    /* RGB: 0, 54, 65 - Verde escuro */
            --sicoob-verde-medio: #7DB61C;     /* RGB: 125, 182, 28 - Verde médio */
            --sicoob-verde-claro: #C9D200;     /* RGB: 201, 210, 0 - Verde claro */
            --sicoob-roxo: #49479D;            /* RGB: 73, 71, 157 - Roxo */
            --sicoob-branco: #FFFFFF;          /* RGB: 255, 255, 255 - Branco */

            /* Cores de Sistema baseadas na identidade oficial */
            --primary-color: var(--sicoob-turquesa);
            --secondary-color: var(--sicoob-verde-escuro);
            --accent-color: var(--sicoob-verde-medio);
            --accent-light: var(--sicoob-verde-claro);
            --accent-purple: var(--sicoob-roxo);
            --success-color: var(--sicoob-verde-medio);
            --warning-color: var(--sicoob-verde-claro);
            --danger-color: #D32F2F;
            --info-color: var(--sicoob-turquesa);
            --dark-color: var(--sicoob-verde-escuro);
            --light-color: #F8FFFE;
            --white-color: var(--sicoob-branco);
            --gray-color: #6B7280;
        }

        /* Layout e Estrutura - Identidade Visual Sicoob Oficial */
        body {
            background: linear-gradient(135deg, var(--light-color) 0%, rgba(0, 160, 145, 0.03) 100%);
            position: relative;
            font-family: 'Open Sans', sans-serif;
            min-height: 100vh;
        }

        /* Navbar - Identidade Visual Oficial Sicoob */
        .navbar {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            box-shadow: 0 4px 20px rgba(0, 54, 65, 0.2);
            border-bottom: 3px solid var(--accent-color);
            position: relative;
            padding: 15px 0;
        }

        .navbar-brand {
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0;
            color: white !important;
        }

        .navbar-brand-img {
            filter: brightness(1.1);
        }

        .btn-outline-light {
            border-color: rgba(255,255,255,0.5);
            color: white;
            transition: all 0.3s ease;
        }

        .btn-outline-light:hover {
            background-color: rgba(255,255,255,0.2);
            border-color: white;
            color: white;
            transform: translateY(-1px);
        }

        /* Elemento gráfico sutil de fundo inspirado nos padrões oficiais Sicoob */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            right: 0;
            width: 300px;
            height: 100vh;
            background: linear-gradient(60deg,
                transparent 0%,
                rgba(0, 160, 145, 0.015) 20%,
                rgba(125, 182, 28, 0.01) 40%,
                rgba(201, 210, 0, 0.008) 60%,
                rgba(73, 71, 157, 0.005) 80%,
                transparent 100%);
            z-index: -1;
            opacity: 0.7;
        }

        /* Container principal */
        .main-container {
            padding: 30px 0;
            min-height: calc(100vh - 120px);
        }

        .status-preview {
            display: inline-flex;
            align-items: center;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            color: white;
            font-weight: 600;
            margin: 0.25rem;
        }
        
        .form-section {
            background: var(--white-color);
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 8px 25px rgba(0, 54, 65, 0.1);
            border: 1px solid rgba(0, 160, 145, 0.1);
            margin-bottom: 2rem;
        }
        
        .table-section {
            background: var(--white-color);
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 8px 25px rgba(0, 54, 65, 0.1);
            border: 1px solid rgba(0, 160, 145, 0.1);
        }
        
        .btn-sicoob {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-sicoob:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 160, 145, 0.3);
            color: white;
        }

        /* Estilos da tabela */
        .table thead th {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border: none;
            font-weight: 600;
            padding: 15px 12px;
            font-size: 0.9rem;
        }

        .table tbody tr {
            transition: all 0.2s ease;
        }

        .table tbody tr:hover {
            background-color: rgba(0, 160, 145, 0.05);
        }

        .table tbody td {
            padding: 15px 12px;
            vertical-align: middle;
            border-color: rgba(0, 160, 145, 0.1);
        }

        /* === ESTILO DO BOTÃO MENU (IGUAL AO DASHBOARD) === */
        .btn-menu-sicoob {
            background: rgba(255, 255, 255, 0.15);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .btn-menu-sicoob:hover {
            background: rgba(255, 255, 255, 0.25);
            border-color: rgba(255, 255, 255, 0.5);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        .btn-menu-sicoob:active {
            transform: translateY(0);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .btn-menu-sicoob i {
            font-size: 0.9rem;
        }

        /* Responsividade */
        @media (max-width: 768px) {
            .main-container {
                padding: 20px 0;
            }

            .form-section, .table-section {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar - Identidade Visual Oficial Sicoob -->
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid px-4">
            <div class="d-flex justify-content-between align-items-center w-100">
                <!-- Logo e Título à Esquerda -->
                <div class="d-flex align-items-center">
                    <img src="../assets/images/logo-sicoob.png" alt="Sicoob" class="navbar-brand-img me-3" style="height: 40px;">
                    <span class="navbar-brand mb-0 h1 text-white">
                        <i class="fas fa-cogs me-2"></i>Gerenciar Status
                    </span>
                </div>

                <!-- Usuário e Botões à Direita -->
                <div class="d-flex align-items-center">
                    <span class="text-white me-3">
                        <i class="fas fa-user-circle me-1"></i>
                        Olá, <?php echo htmlspecialchars($usuario['nome_completo']); ?>
                    </span>

                    <a href="index.php" class="btn btn-menu-sicoob me-2">
                        <i class="fas fa-arrow-left me-2"></i>
                        <span>Voltar aos Planos</span>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Conteúdo Principal -->
    <div class="main-container">
        <div class="container-fluid">
        <!-- Alertas -->
        <?php if ($erro): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo htmlspecialchars($erro); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if ($sucesso): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo htmlspecialchars($sucesso); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Formulário -->
        <div class="form-section">
            <h3 class="mb-4">
                <i class="fas fa-<?php echo $status_edicao ? 'edit' : 'plus'; ?> me-2"></i>
                <?php echo $status_edicao ? 'Editar Status' : 'Novo Status'; ?>
            </h3>
            
            <form method="POST">
                <input type="hidden" name="acao" value="<?php echo $status_edicao ? 'editar' : 'criar'; ?>">
                <?php if ($status_edicao): ?>
                <input type="hidden" name="id" value="<?php echo $status_edicao['id']; ?>">
                <?php endif; ?>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="nome" class="form-label fw-bold">Nome do Status *</label>
                            <input type="text" class="form-control" id="nome" name="nome" 
                                   value="<?php echo $status_edicao ? htmlspecialchars($status_edicao['nome']) : ''; ?>" required>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="cor" class="form-label fw-bold">Cor *</label>
                            <input type="color" class="form-control form-control-color" id="cor" name="cor" 
                                   value="<?php echo $status_edicao ? $status_edicao['cor'] : '#00A091'; ?>" required>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="ordem" class="form-label fw-bold">Ordem *</label>
                            <input type="number" class="form-control" id="ordem" name="ordem" min="1"
                                   value="<?php echo $status_edicao ? $status_edicao['ordem'] : '1'; ?>" required>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="icone" class="form-label fw-bold">Ícone (Font Awesome) *</label>
                            <select class="form-select" id="icone" name="icone" required>
                                <option value="">Selecione um ícone...</option>
                                <option value="fas fa-play" <?php echo ($status_edicao && $status_edicao['icone'] == 'fas fa-play') ? 'selected' : ''; ?>>
                                    ▶️ Iniciar/Em Andamento (fas fa-play)
                                </option>
                                <option value="fas fa-pause" <?php echo ($status_edicao && $status_edicao['icone'] == 'fas fa-pause') ? 'selected' : ''; ?>>
                                    ⏸️ Pausado (fas fa-pause)
                                </option>
                                <option value="fas fa-stop" <?php echo ($status_edicao && $status_edicao['icone'] == 'fas fa-stop') ? 'selected' : ''; ?>>
                                    ⏹️ Parado (fas fa-stop)
                                </option>
                                <option value="fas fa-check" <?php echo ($status_edicao && $status_edicao['icone'] == 'fas fa-check') ? 'selected' : ''; ?>>
                                    ✅ Concluído (fas fa-check)
                                </option>
                                <option value="fas fa-check-circle" <?php echo ($status_edicao && $status_edicao['icone'] == 'fas fa-check-circle') ? 'selected' : ''; ?>>
                                    ✅ Finalizado (fas fa-check-circle)
                                </option>
                                <option value="fas fa-times" <?php echo ($status_edicao && $status_edicao['icone'] == 'fas fa-times') ? 'selected' : ''; ?>>
                                    ❌ Cancelado (fas fa-times)
                                </option>
                                <option value="fas fa-times-circle" <?php echo ($status_edicao && $status_edicao['icone'] == 'fas fa-times-circle') ? 'selected' : ''; ?>>
                                    ❌ Rejeitado (fas fa-times-circle)
                                </option>
                                <option value="fas fa-clock" <?php echo ($status_edicao && $status_edicao['icone'] == 'fas fa-clock') ? 'selected' : ''; ?>>
                                    ⏰ Aguardando (fas fa-clock)
                                </option>
                                <option value="fas fa-hourglass-half" <?php echo ($status_edicao && $status_edicao['icone'] == 'fas fa-hourglass-half') ? 'selected' : ''; ?>>
                                    ⏳ Em Progresso (fas fa-hourglass-half)
                                </option>
                                <option value="fas fa-exclamation-triangle" <?php echo ($status_edicao && $status_edicao['icone'] == 'fas fa-exclamation-triangle') ? 'selected' : ''; ?>>
                                    ⚠️ Atenção (fas fa-exclamation-triangle)
                                </option>
                                <option value="fas fa-info-circle" <?php echo ($status_edicao && $status_edicao['icone'] == 'fas fa-info-circle') ? 'selected' : ''; ?>>
                                    ℹ️ Informação (fas fa-info-circle)
                                </option>
                                <option value="fas fa-circle" <?php echo (!$status_edicao || $status_edicao['icone'] == 'fas fa-circle') ? 'selected' : ''; ?>>
                                    ⚫ Círculo (fas fa-circle)
                                </option>
                                <option value="fas fa-dot-circle" <?php echo ($status_edicao && $status_edicao['icone'] == 'fas fa-dot-circle') ? 'selected' : ''; ?>>
                                    🔘 Ponto (fas fa-dot-circle)
                                </option>
                                <option value="fas fa-star" <?php echo ($status_edicao && $status_edicao['icone'] == 'fas fa-star') ? 'selected' : ''; ?>>
                                    ⭐ Prioridade (fas fa-star)
                                </option>
                                <option value="fas fa-flag" <?php echo ($status_edicao && $status_edicao['icone'] == 'fas fa-flag') ? 'selected' : ''; ?>>
                                    🚩 Marco (fas fa-flag)
                                </option>
                                <option value="fas fa-thumbs-up" <?php echo ($status_edicao && $status_edicao['icone'] == 'fas fa-thumbs-up') ? 'selected' : ''; ?>>
                                    👍 Aprovado (fas fa-thumbs-up)
                                </option>
                                <option value="fas fa-thumbs-down" <?php echo ($status_edicao && $status_edicao['icone'] == 'fas fa-thumbs-down') ? 'selected' : ''; ?>>
                                    👎 Reprovado (fas fa-thumbs-down)
                                </option>
                                <option value="fas fa-sync" <?php echo ($status_edicao && $status_edicao['icone'] == 'fas fa-sync') ? 'selected' : ''; ?>>
                                    🔄 Revisão (fas fa-sync)
                                </option>
                                <option value="fas fa-edit" <?php echo ($status_edicao && $status_edicao['icone'] == 'fas fa-edit') ? 'selected' : ''; ?>>
                                    ✏️ Em Edição (fas fa-edit)
                                </option>
                                <option value="fas fa-eye" <?php echo ($status_edicao && $status_edicao['icone'] == 'fas fa-eye') ? 'selected' : ''; ?>>
                                    👁️ Em Análise (fas fa-eye)
                                </option>
                            </select>
                            <div class="form-text">Selecione o ícone que melhor representa o status</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="descricao" class="form-label fw-bold">Descrição</label>
                            <textarea class="form-control" id="descricao" name="descricao" rows="3"
                                      placeholder="Descrição do status..."><?php echo $status_edicao ? htmlspecialchars($status_edicao['descricao']) : ''; ?></textarea>
                        </div>
                    </div>
                </div>
                
                <!-- Preview -->
                <div class="mb-3">
                    <label class="form-label fw-bold">Preview:</label>
                    <div id="status-preview" class="status-preview" style="background-color: #00A091;">
                        <i class="fas fa-circle me-2"></i>
                        <span>Nome do Status</span>
                    </div>
                </div>
                
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-sicoob">
                        <i class="fas fa-save me-2"></i>
                        <?php echo $status_edicao ? 'Atualizar' : 'Criar'; ?> Status
                    </button>
                    
                    <?php if ($status_edicao): ?>
                    <a href="admin_status.php" class="btn btn-secondary">
                        <i class="fas fa-times me-2"></i>Cancelar
                    </a>
                    <?php endif; ?>
                </div>
            </form>
        </div>

        <!-- Lista de Status -->
        <div class="table-section">
            <h3 class="mb-4">
                <i class="fas fa-list me-2"></i>
                Status Cadastrados (<?php echo count($status_list); ?>)
            </h3>
            
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>Preview</th>
                            <th>Nome</th>
                            <th>Descrição</th>
                            <th>Ordem</th>
                            <th>Status</th>
                            <th>Planos</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($status_list as $status): ?>
                        <?php
                        // Contar quantos planos usam este status
                        $stmt = $pdo->prepare("SELECT COUNT(*) FROM planos WHERE status_id = ?");
                        $stmt->execute([$status['id']]);
                        $total_planos = $stmt->fetchColumn();
                        ?>
                        <tr>
                            <td>
                                <span class="status-preview" style="background-color: <?php echo $status['cor']; ?>;">
                                    <i class="<?php echo $status['icone']; ?> me-1"></i>
                                    <?php echo htmlspecialchars($status['nome']); ?>
                                </span>
                            </td>
                            <td class="fw-bold"><?php echo htmlspecialchars($status['nome']); ?></td>
                            <td><?php echo htmlspecialchars($status['descricao'] ?: '-'); ?></td>
                            <td><span class="badge bg-secondary"><?php echo $status['ordem']; ?></span></td>
                            <td>
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="acao" value="toggle_ativo">
                                    <input type="hidden" name="id" value="<?php echo $status['id']; ?>">
                                    <button type="submit" class="btn btn-sm <?php echo $status['ativo'] ? 'btn-success' : 'btn-warning'; ?>">
                                        <i class="fas fa-<?php echo $status['ativo'] ? 'check' : 'pause'; ?>"></i>
                                        <?php echo $status['ativo'] ? 'Ativo' : 'Inativo'; ?>
                                    </button>
                                </form>
                            </td>
                            <td>
                                <span class="badge bg-info"><?php echo $total_planos; ?> planos</span>
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <a href="admin_status.php?editar=<?php echo $status['id']; ?>" class="btn btn-outline-primary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    
                                    <?php if ($total_planos == 0): ?>
                                    <button type="button" class="btn btn-outline-danger" 
                                            onclick="confirmarExclusao(<?php echo $status['id']; ?>, '<?php echo htmlspecialchars($status['nome']); ?>')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    <?php else: ?>
                                    <button type="button" class="btn btn-outline-secondary" disabled title="Status em uso">
                                        <i class="fas fa-lock"></i>
                                    </button>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    </div>

    <!-- Modal de Confirmação -->
    <div class="modal fade" id="modalExcluir" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirmar Exclusão</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Tem certeza que deseja excluir o status <strong id="nomeStatus"></strong>?</p>
                    <p class="text-danger"><i class="fas fa-exclamation-triangle me-2"></i>Esta ação não pode ser desfeita.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="acao" value="excluir">
                        <input type="hidden" name="id" id="idExcluir">
                        <button type="submit" class="btn btn-danger">Excluir</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Preview do status
        function atualizarPreview() {
            const nome = document.getElementById('nome').value || 'Nome do Status';
            const cor = document.getElementById('cor').value;
            const icone = document.getElementById('icone').value || 'fas fa-circle';
            
            const preview = document.getElementById('status-preview');
            preview.style.backgroundColor = cor;
            preview.innerHTML = `<i class="${icone} me-2"></i><span>${nome}</span>`;
        }
        
        // Atualizar preview em tempo real
        document.getElementById('nome').addEventListener('input', atualizarPreview);
        document.getElementById('cor').addEventListener('input', atualizarPreview);
        document.getElementById('icone').addEventListener('change', atualizarPreview);
        
        // Inicializar preview
        atualizarPreview();
        
        // Função para confirmar exclusão
        function confirmarExclusao(id, nome) {
            document.getElementById('nomeStatus').textContent = nome;
            document.getElementById('idExcluir').value = id;
            new bootstrap.Modal(document.getElementById('modalExcluir')).show();
        }
    </script>
</body>
</html>
