<?php
require_once 'config/database.php';

echo "<h2>🔍 Verificação do Sistema de Logs</h2>";

try {
    // Verificar tabelas de log existentes
    echo "<h3>📋 Tabelas de Log Existentes</h3>";
    $stmt = $pdo->query("SHOW TABLES LIKE '%log%'");
    $tabelas_log = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (empty($tabelas_log)) {
        echo "<div class='alert alert-warning'>⚠️ Nenhuma tabela de log específica encontrada.</div>";
    } else {
        echo "<div class='alert alert-info'>📊 Tabelas de log encontradas:</div>";
        echo "<ul>";
        foreach ($tabelas_log as $tabela) {
            echo "<li><strong>{$tabela}</strong></li>";
        }
        echo "</ul>";
    }
    
    // Verificar tabelas relacionadas a planos
    echo "<h3>🎯 Tabelas do Sistema de Planos</h3>";
    $stmt = $pdo->query("SHOW TABLES LIKE 'planos%'");
    $tabelas_planos = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    echo "<div class='alert alert-info'>📊 Tabelas de planos encontradas:</div>";
    echo "<ul>";
    foreach ($tabelas_planos as $tabela) {
        echo "<li><strong>{$tabela}</strong>";
        
        // Verificar se é tabela de histórico/log
        if (strpos($tabela, 'historico') !== false || strpos($tabela, 'log') !== false) {
            echo " <span class='badge bg-success'>LOG/HISTÓRICO</span>";
        }
        echo "</li>";
    }
    echo "</ul>";
    
    // Verificar se existe tabela de log geral
    echo "<h3>🗂️ Verificando Tabela de Log Geral</h3>";
    $stmt = $pdo->query("SHOW TABLES LIKE 'logs'");
    $tabela_logs_geral = $stmt->fetch();
    
    if ($tabela_logs_geral) {
        echo "<div class='alert alert-success'>✅ Tabela 'logs' encontrada!</div>";
        
        // Verificar estrutura
        $stmt = $pdo->query("DESCRIBE logs");
        $estrutura = $stmt->fetchAll();
        
        echo "<h4>Estrutura da tabela 'logs':</h4>";
        echo "<table class='table table-striped'>";
        echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Chave</th><th>Padrão</th></tr>";
        foreach ($estrutura as $campo) {
            echo "<tr>";
            echo "<td>{$campo['Field']}</td>";
            echo "<td>{$campo['Type']}</td>";
            echo "<td>{$campo['Null']}</td>";
            echo "<td>{$campo['Key']}</td>";
            echo "<td>{$campo['Default']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Verificar últimos registros
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM logs");
        $total = $stmt->fetch()['total'];
        echo "<p><strong>Total de registros:</strong> {$total}</p>";
        
        if ($total > 0) {
            $stmt = $pdo->query("SELECT * FROM logs ORDER BY created_at DESC LIMIT 5");
            $ultimos = $stmt->fetchAll();
            
            echo "<h4>Últimos 5 registros:</h4>";
            echo "<table class='table table-sm'>";
            echo "<tr><th>ID</th><th>Usuário</th><th>Ação</th><th>Detalhes</th><th>Data</th></tr>";
            foreach ($ultimos as $log) {
                echo "<tr>";
                echo "<td>{$log['id']}</td>";
                echo "<td>{$log['usuario_id']}</td>";
                echo "<td>{$log['acao']}</td>";
                echo "<td>" . substr($log['detalhes'], 0, 50) . "...</td>";
                echo "<td>{$log['created_at']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } else {
        echo "<div class='alert alert-warning'>⚠️ Tabela 'logs' não encontrada. Será necessário criar.</div>";
    }
    
    // Verificar histórico de status dos planos
    echo "<h3>📈 Histórico de Status dos Planos</h3>";
    $stmt = $pdo->query("SHOW TABLES LIKE 'planos_historico_status'");
    $tabela_historico = $stmt->fetch();
    
    if ($tabela_historico) {
        echo "<div class='alert alert-success'>✅ Tabela 'planos_historico_status' encontrada!</div>";
        
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM planos_historico_status");
        $total_historico = $stmt->fetch()['total'];
        echo "<p><strong>Total de registros no histórico:</strong> {$total_historico}</p>";
        
        if ($total_historico > 0) {
            $stmt = $pdo->query("
                SELECT h.*, u.nome_completo, ps.nome as status_nome 
                FROM planos_historico_status h
                LEFT JOIN usuarios u ON h.usuario_id = u.id
                LEFT JOIN planos_status ps ON h.status_novo_id = ps.id
                ORDER BY h.created_at DESC 
                LIMIT 5
            ");
            $historico = $stmt->fetchAll();
            
            echo "<h4>Últimas 5 mudanças de status:</h4>";
            echo "<table class='table table-sm'>";
            echo "<tr><th>Plano ID</th><th>Usuário</th><th>Novo Status</th><th>Observação</th><th>Data</th></tr>";
            foreach ($historico as $h) {
                echo "<tr>";
                echo "<td>{$h['plano_id']}</td>";
                echo "<td>{$h['nome_completo']}</td>";
                echo "<td>{$h['status_nome']}</td>";
                echo "<td>" . substr($h['observacao'], 0, 30) . "...</td>";
                echo "<td>{$h['created_at']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } else {
        echo "<div class='alert alert-danger'>❌ Tabela 'planos_historico_status' não encontrada!</div>";
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ Erro: " . $e->getMessage() . "</div>";
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background: #f8f9fa;
    line-height: 1.6;
}

h2, h3, h4 {
    color: #003641;
    margin-bottom: 15px;
}

.alert {
    padding: 15px;
    margin: 15px 0;
    border-radius: 8px;
    border: 1px solid transparent;
}

.alert-success {
    background: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.alert-warning {
    background: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.alert-info {
    background: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

.alert-danger {
    background: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.table {
    width: 100%;
    border-collapse: collapse;
    margin: 15px 0;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.table th, .table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.table th {
    background: #003641;
    color: white;
    font-weight: 600;
}

.table-striped tr:nth-child(even) {
    background: #f8f9fa;
}

.table-sm th, .table-sm td {
    padding: 8px;
}

.badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.bg-success {
    background-color: #28a745;
    color: white;
}

ul {
    margin: 10px 0;
    padding-left: 20px;
}

li {
    margin: 5px 0;
}
</style>
