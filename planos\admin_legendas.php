<?php
require_once '../auth_check.php';
require_once '../config/database.php';
require_once 'PlanosLogger.php';

// Verificar permissões de acesso
$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ?");
$stmt->execute([$_SESSION['user_id']]);
$usuario = $stmt->fetch(PDO::FETCH_ASSOC);

$is_admin = in_array($usuario['nivel_acesso_id'], [1, 2]);

if ($is_admin) {
    $tem_permissao = true;
} else {
    // Verificar permissões de acesso aos planos (usuário direto ou por setor)
    $tem_permissao = false;

    try {
        // 1. Verificar se tem acesso direto por usuário a qualquer botão
        $stmt = $pdo->prepare("
            SELECT COUNT(*)
            FROM card_button_usuarios cbu
            WHERE cbu.usuario_id = ?
        ");
        $stmt->execute([$_SESSION['user_id']]);
        $tem_permissao_usuario = $stmt->fetchColumn() > 0;

        if ($tem_permissao_usuario) {
            $tem_permissao = true;
        } else {
            // 2. Verificar se tem acesso por setor a qualquer botão
            $stmt = $pdo->prepare("
                SELECT COUNT(*)
                FROM card_button_setores cbs
                INNER JOIN usuario_setor us ON cbs.setor_id = us.setor_id
                WHERE us.usuario_id = ?
            ");
            $stmt->execute([$_SESSION['user_id']]);
            $tem_permissao_setor = $stmt->fetchColumn() > 0;

            if ($tem_permissao_setor) {
                $tem_permissao = true;
            }
        }

    } catch (Exception $e) {
        // Se der erro, permitir acesso para usuários logados
        $tem_permissao = true;
    }
}

if (!$tem_permissao) {
    header('Location: index.php?error=acesso_negado');
    exit;
}

$erro = '';
$sucesso = '';

// Processar ações
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $logger = new PlanosLogger($pdo);

        if (isset($_POST['acao'])) {
            switch ($_POST['acao']) {
                case 'criar':
                    $stmt = $pdo->prepare("INSERT INTO planos_legendas (nome, cor, descricao, ordem) VALUES (?, ?, ?, ?)");
                    $stmt->execute([
                        $_POST['nome'],
                        $_POST['cor'],
                        $_POST['descricao'],
                        $_POST['ordem']
                    ]);
                    $legenda_id = $pdo->lastInsertId();

                    // Log da criação
                    $logger->logAcaoAdmin($_SESSION['user_id'], 'criar_legenda', [
                        'legenda_id' => $legenda_id,
                        'nome' => $_POST['nome'],
                        'cor' => $_POST['cor'],
                        'descricao' => $_POST['descricao']
                    ]);

                    $sucesso = 'Objetivo estratégico criado com sucesso!';
                    break;
                    
                case 'editar':
                    $stmt = $pdo->prepare("UPDATE planos_legendas SET nome = ?, cor = ?, descricao = ?, ordem = ? WHERE id = ?");
                    $stmt->execute([
                        $_POST['nome'],
                        $_POST['cor'],
                        $_POST['descricao'],
                        $_POST['ordem'],
                        $_POST['id']
                    ]);
                    $sucesso = 'Objetivo estratégico atualizado com sucesso!';
                    break;
                    
                case 'excluir':
                    // Verificar se a legenda está sendo usada
                    $stmt = $pdo->prepare("SELECT COUNT(*) FROM planos WHERE legenda_id = ?");
                    $stmt->execute([$_POST['id']]);
                    $em_uso = $stmt->fetchColumn();
                    
                    if ($em_uso > 0) {
                        $erro = "Não é possível excluir este objetivo estratégico pois está sendo usado por {$em_uso} plano(s).";
                    } else {
                        $stmt = $pdo->prepare("DELETE FROM planos_legendas WHERE id = ?");
                        $stmt->execute([$_POST['id']]);
                        $sucesso = 'Objetivo estratégico excluído com sucesso!';
                    }
                    break;
                    
                case 'toggle_ativo':
                    $stmt = $pdo->prepare("UPDATE planos_legendas SET ativo = NOT ativo WHERE id = ?");
                    $stmt->execute([$_POST['id']]);
                    $sucesso = 'Objetivo estratégico atualizado com sucesso!';
                    break;
            }
        }
    } catch (Exception $e) {
        $erro = 'Erro: ' . $e->getMessage();
    }
}

// Buscar todas as legendas
$stmt = $pdo->query("SELECT * FROM planos_legendas ORDER BY ordem, nome");
$legendas_list = $stmt->fetchAll();

// Buscar legenda para edição
$legenda_edicao = null;
if (isset($_GET['editar'])) {
    $stmt = $pdo->prepare("SELECT * FROM planos_legendas WHERE id = ?");
    $stmt->execute([$_GET['editar']]);
    $legenda_edicao = $stmt->fetch();
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gerenciar Objetivos Estratégicos - Sistema Sicoob</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="../assets/images/Sicoob.ico">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            /* Paleta de Cores Oficial Sicoob 2024 - Manual de Marca */
            --sicoob-turquesa: #00A091;        /* RGB: 0, 160, 145 - Cor principal */
            --sicoob-verde-escuro: #003641;    /* RGB: 0, 54, 65 - Verde escuro */
            --sicoob-verde-medio: #7DB61C;     /* RGB: 125, 182, 28 - Verde médio */
            --sicoob-verde-claro: #C9D200;     /* RGB: 201, 210, 0 - Verde claro */
            --sicoob-roxo: #49479D;            /* RGB: 73, 71, 157 - Roxo */
            --sicoob-branco: #FFFFFF;          /* RGB: 255, 255, 255 - Branco */

            /* Cores de Sistema baseadas na identidade oficial */
            --primary-color: var(--sicoob-turquesa);
            --secondary-color: var(--sicoob-verde-escuro);
            --accent-color: var(--sicoob-verde-medio);
            --accent-light: var(--sicoob-verde-claro);
            --accent-purple: var(--sicoob-roxo);
            --success-color: var(--sicoob-verde-medio);
            --warning-color: var(--sicoob-verde-claro);
            --danger-color: #D32F2F;
            --info-color: var(--sicoob-turquesa);
            --dark-color: var(--sicoob-verde-escuro);
            --light-color: #F8FFFE;
            --white-color: var(--sicoob-branco);
            --gray-color: #6B7280;
        }

        /* Layout e Estrutura - Identidade Visual Sicoob Oficial */
        body {
            background: linear-gradient(135deg, var(--light-color) 0%, rgba(0, 160, 145, 0.03) 100%);
            position: relative;
            font-family: 'Open Sans', sans-serif;
            min-height: 100vh;
        }

        /* Navbar - Identidade Visual Oficial Sicoob */
        .navbar {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            box-shadow: 0 4px 20px rgba(0, 54, 65, 0.2);
            border-bottom: 3px solid var(--accent-color);
            position: relative;
            padding: 15px 0;
        }

        .navbar-brand {
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0;
            color: white !important;
        }

        .navbar-brand-img {
            filter: brightness(1.1);
        }

        .btn-outline-light {
            border-color: rgba(255,255,255,0.5);
            color: white;
            transition: all 0.3s ease;
        }

        .btn-outline-light:hover {
            background-color: rgba(255,255,255,0.2);
            border-color: white;
            color: white;
            transform: translateY(-1px);
        }

        /* Elemento gráfico sutil de fundo inspirado nos padrões oficiais Sicoob */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            right: 0;
            width: 300px;
            height: 100vh;
            background: linear-gradient(60deg,
                transparent 0%,
                rgba(0, 160, 145, 0.015) 20%,
                rgba(125, 182, 28, 0.01) 40%,
                rgba(201, 210, 0, 0.008) 60%,
                rgba(73, 71, 157, 0.005) 80%,
                transparent 100%);
            z-index: -1;
            opacity: 0.7;
        }

        /* Container principal */
        .main-container {
            padding: 30px 0;
            min-height: calc(100vh - 120px);
        }

        .legenda-preview {
            display: inline-flex;
            align-items: center;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            color: white;
            font-weight: 600;
            margin: 0.25rem;
        }
        
        .form-section {
            background: var(--white-color);
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 8px 25px rgba(0, 54, 65, 0.1);
            border: 1px solid rgba(0, 160, 145, 0.1);
            margin-bottom: 2rem;
        }
        
        .table-section {
            background: var(--white-color);
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 8px 25px rgba(0, 54, 65, 0.1);
            border: 1px solid rgba(0, 160, 145, 0.1);
        }
        
        .btn-sicoob {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-sicoob:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 160, 145, 0.3);
            color: white;
        }
        
        .legenda-card {
            border: 2px solid rgba(0, 160, 145, 0.1);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
            background: var(--white-color);
            box-shadow: 0 4px 15px rgba(0, 54, 65, 0.08);
        }
        
        .legenda-card:hover {
            border-color: var(--primary-color);
            box-shadow: 0 8px 25px rgba(0, 54, 65, 0.15);
            transform: translateY(-3px);
        }

        /* === ESTILO DO BOTÃO MENU (IGUAL AO DASHBOARD) === */
        .btn-menu-sicoob {
            background: rgba(255, 255, 255, 0.15);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .btn-menu-sicoob:hover {
            background: rgba(255, 255, 255, 0.25);
            border-color: rgba(255, 255, 255, 0.5);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        .btn-menu-sicoob:active {
            transform: translateY(0);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .btn-menu-sicoob i {
            font-size: 0.9rem;
        }

        /* Responsividade */
        @media (max-width: 768px) {
            .main-container {
                padding: 20px 0;
            }

            .form-section, .table-section {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar - Identidade Visual Oficial Sicoob -->
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid px-4">
            <div class="d-flex justify-content-between align-items-center w-100">
                <!-- Logo e Título à Esquerda -->
                <div class="d-flex align-items-center">
                    <img src="../assets/images/logo-sicoob.png" alt="Sicoob" class="navbar-brand-img me-3" style="height: 40px;">
                    <span class="navbar-brand mb-0 h1 text-white">
                        <i class="fas fa-bullseye me-2"></i>Objetivos Estratégicos
                    </span>
                </div>

                <!-- Usuário e Botões à Direita -->
                <div class="d-flex align-items-center">
                    <span class="text-white me-3">
                        <i class="fas fa-user-circle me-1"></i>
                        Olá, <?php echo htmlspecialchars($usuario['nome_completo']); ?>
                    </span>

                    <a href="index.php" class="btn btn-menu-sicoob me-2">
                        <i class="fas fa-arrow-left me-2"></i>
                        <span>Voltar aos Planos</span>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Conteúdo Principal -->
    <div class="main-container">
        <div class="container-fluid">
        <!-- Alertas -->
        <?php if ($erro): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <?php echo htmlspecialchars($erro); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <?php if ($sucesso): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i>
            <?php echo htmlspecialchars($sucesso); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        <?php endif; ?>

        <!-- Formulário -->
        <div class="form-section">
            <h3 class="mb-4">
                <i class="fas fa-<?php echo $legenda_edicao ? 'edit' : 'plus'; ?> me-2"></i>
                <?php echo $legenda_edicao ? 'Editar Objetivo Estratégico' : 'Novo Objetivo Estratégico'; ?>
            </h3>
            
            <form method="POST">
                <input type="hidden" name="acao" value="<?php echo $legenda_edicao ? 'editar' : 'criar'; ?>">
                <?php if ($legenda_edicao): ?>
                <input type="hidden" name="id" value="<?php echo $legenda_edicao['id']; ?>">
                <?php endif; ?>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="nome" class="form-label fw-bold">Nome do Objetivo Estratégico *</label>
                            <input type="text" class="form-control" id="nome" name="nome" 
                                   value="<?php echo $legenda_edicao ? htmlspecialchars($legenda_edicao['nome']) : ''; ?>" required>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="cor" class="form-label fw-bold">Cor *</label>
                            <input type="color" class="form-control form-control-color" id="cor" name="cor" 
                                   value="<?php echo $legenda_edicao ? $legenda_edicao['cor'] : '#2E7D32'; ?>" required>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="ordem" class="form-label fw-bold">Ordem *</label>
                            <input type="number" class="form-control" id="ordem" name="ordem" min="1"
                                   value="<?php echo $legenda_edicao ? $legenda_edicao['ordem'] : '1'; ?>" required>
                        </div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label for="descricao" class="form-label fw-bold">Descrição *</label>
                    <textarea class="form-control" id="descricao" name="descricao" rows="3" required
                              placeholder="Descreva o objetivo estratégico..."><?php echo $legenda_edicao ? htmlspecialchars($legenda_edicao['descricao']) : ''; ?></textarea>
                </div>
                
                <!-- Preview -->
                <div class="mb-3">
                    <label class="form-label fw-bold">Preview:</label>
                    <div id="legenda-preview" class="legenda-preview" style="background-color: #2E7D32;">
                        <i class="fas fa-bullseye me-2"></i>
                        <span>Nome do Objetivo</span>
                    </div>
                </div>
                
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-sicoob">
                        <i class="fas fa-save me-2"></i>
                        <?php echo $legenda_edicao ? 'Atualizar' : 'Criar'; ?> Objetivo
                    </button>
                    
                    <?php if ($legenda_edicao): ?>
                    <a href="admin_legendas.php" class="btn btn-secondary">
                        <i class="fas fa-times me-2"></i>Cancelar
                    </a>
                    <?php endif; ?>
                </div>
            </form>
        </div>

        <!-- Lista de Objetivos Estratégicos -->
        <div class="table-section">
            <h3 class="mb-4">
                <i class="fas fa-list me-2"></i>
                Objetivos Estratégicos Cadastrados (<?php echo count($legendas_list); ?>)
            </h3>
            
            <div class="row">
                <?php foreach ($legendas_list as $legenda): ?>
                <?php
                // Contar quantos planos usam esta legenda
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM planos WHERE legenda_id = ?");
                $stmt->execute([$legenda['id']]);
                $total_planos = $stmt->fetchColumn();
                ?>
                <div class="col-md-6 col-lg-4">
                    <div class="legenda-card">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <div class="legenda-preview" style="background-color: <?php echo $legenda['cor']; ?>;">
                                <i class="fas fa-bullseye me-2"></i>
                                <?php echo htmlspecialchars($legenda['nome']); ?>
                            </div>
                            <span class="badge bg-secondary">Ordem: <?php echo $legenda['ordem']; ?></span>
                        </div>
                        
                        <p class="text-muted mb-3"><?php echo htmlspecialchars($legenda['descricao']); ?></p>
                        
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <span class="badge bg-info me-2"><?php echo $total_planos; ?> planos</span>
                                <span class="badge <?php echo $legenda['ativo'] ? 'bg-success' : 'bg-warning'; ?>">
                                    <?php echo $legenda['ativo'] ? 'Ativo' : 'Inativo'; ?>
                                </span>
                            </div>
                            
                            <div class="btn-group btn-group-sm">
                                <a href="admin_legendas.php?editar=<?php echo $legenda['id']; ?>" class="btn btn-outline-primary">
                                    <i class="fas fa-edit"></i>
                                </a>
                                
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="acao" value="toggle_ativo">
                                    <input type="hidden" name="id" value="<?php echo $legenda['id']; ?>">
                                    <button type="submit" class="btn btn-outline-<?php echo $legenda['ativo'] ? 'warning' : 'success'; ?>">
                                        <i class="fas fa-<?php echo $legenda['ativo'] ? 'pause' : 'play'; ?>"></i>
                                    </button>
                                </form>
                                
                                <?php if ($total_planos == 0): ?>
                                <button type="button" class="btn btn-outline-danger" 
                                        onclick="confirmarExclusao(<?php echo $legenda['id']; ?>, '<?php echo htmlspecialchars($legenda['nome']); ?>')">
                                    <i class="fas fa-trash"></i>
                                </button>
                                <?php else: ?>
                                <button type="button" class="btn btn-outline-secondary" disabled title="Objetivo em uso">
                                    <i class="fas fa-lock"></i>
                                </button>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
    </div>

    <!-- Modal de Confirmação -->
    <div class="modal fade" id="modalExcluir" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirmar Exclusão</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Tem certeza que deseja excluir o objetivo estratégico <strong id="nomeLegenda"></strong>?</p>
                    <p class="text-danger"><i class="fas fa-exclamation-triangle me-2"></i>Esta ação não pode ser desfeita.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="acao" value="excluir">
                        <input type="hidden" name="id" id="idExcluir">
                        <button type="submit" class="btn btn-danger">Excluir</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Preview da legenda
        function atualizarPreview() {
            const nome = document.getElementById('nome').value || 'Nome do Objetivo';
            const cor = document.getElementById('cor').value;
            
            const preview = document.getElementById('legenda-preview');
            preview.style.backgroundColor = cor;
            preview.innerHTML = `<i class="fas fa-bullseye me-2"></i><span>${nome}</span>`;
        }
        
        // Atualizar preview em tempo real
        document.getElementById('nome').addEventListener('input', atualizarPreview);
        document.getElementById('cor').addEventListener('input', atualizarPreview);
        
        // Inicializar preview
        atualizarPreview();
        
        // Função para confirmar exclusão
        function confirmarExclusao(id, nome) {
            document.getElementById('nomeLegenda').textContent = nome;
            document.getElementById('idExcluir').value = id;
            new bootstrap.Modal(document.getElementById('modalExcluir')).show();
        }
    </script>
</body>
</html>
