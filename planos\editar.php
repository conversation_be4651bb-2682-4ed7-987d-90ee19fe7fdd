<?php
require_once '../auth_check.php';
require_once '../config/database.php';
require_once 'PlanosLogger.php';

// Verificar se o usuário está logado
if (!isset($_SESSION['user_id'])) {
    header('Location: ../login.php');
    exit;
}

// Verificar se foi passado um ID do plano
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header('Location: index.php');
    exit;
}

$plano_id = $_GET['id'];

// Processar formulário de edição
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['salvar_plano'])) {
    try {
        $stmt = $pdo->prepare("
            UPDATE planos SET
                nome = ?,
                oque = ?,
                porque = ?,
                onde = ?,
                quando_inicio = ?,
                quando_fim = ?,
                como = ?,
                quanto_custa = ?,
                resultados = ?,
                legenda_id = ?,
                updated_at = NOW()
            WHERE id = ?
        ");
        
        $stmt->execute([
            $_POST['nome'],
            $_POST['oque'],
            $_POST['porque'],
            $_POST['onde'],
            $_POST['quando_inicio'],
            $_POST['quando_fim'],
            $_POST['como'],
            str_replace(',', '.', str_replace('.', '', $_POST['quanto_custa'])),
            $_POST['resultados'],
            !empty($_POST['legenda_id']) ? $_POST['legenda_id'] : null,
            $plano_id
        ]);

        // Registrar log da edição
        $logger = new PlanosLogger($pdo);
        $dados_novos = [
            'nome' => $_POST['nome'],
            'oque' => $_POST['oque'],
            'porque' => $_POST['porque'],
            'onde' => $_POST['onde'],
            'quando_inicio' => $_POST['quando_inicio'],
            'quando_fim' => $_POST['quando_fim'],
            'como' => $_POST['como'],
            'quanto_custa' => str_replace(',', '.', str_replace('.', '', $_POST['quanto_custa'])),
            'resultados' => $_POST['resultados'],
            'legenda_id' => !empty($_POST['legenda_id']) ? $_POST['legenda_id'] : null
        ];
        $logger->logEditarPlano($_SESSION['user_id'], $plano_id, $plano, $dados_novos);

        // Adicionar interação sobre a edição
        $stmt = $pdo->prepare("
            INSERT INTO planos_interacoes (plano_id, usuario_id, tipo, conteudo, created_at)
            VALUES (?, ?, 'atualizacao', 'Plano de ação foi editado e atualizado.', NOW())
        ");
        $stmt->execute([$plano_id, $_SESSION['user_id']]);
        
        header("Location: visualizar.php?id={$plano_id}&success=plano_editado");
        exit;
        
    } catch (Exception $e) {
        $erro = "Erro ao salvar plano: " . $e->getMessage();
    }
}

// Buscar dados do plano
try {
    $stmt = $pdo->prepare("SELECT * FROM planos WHERE id = ?");
    $stmt->execute([$plano_id]);
    $plano = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$plano) {
        header('Location: index.php?error=plano_nao_encontrado');
        exit;
    }
} catch (Exception $e) {
    header('Location: index.php?error=erro_buscar_plano');
    exit;
}

// Buscar dados do usuário logado
$stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ?");
$stmt->execute([$_SESSION['user_id']]);
$usuario = $stmt->fetch(PDO::FETCH_ASSOC);

// Buscar legendas disponíveis
$stmt_legendas = $pdo->query("SELECT * FROM planos_legendas WHERE ativo = 1 ORDER BY ordem, nome");
$legendas_list = $stmt_legendas->fetchAll();
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Editar Plano - Sistema Sicoob</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        :root {
            /* Paleta de Cores Oficial Sicoob 2024 */
            --sicoob-turquesa: #00A091;
            --sicoob-verde-escuro: #003641;
            --sicoob-verde-medio: #7DB61C;
            --sicoob-verde-claro: #C9D200;
            --sicoob-roxo: #49479D;
            --sicoob-branco: #FFFFFF;
            
            --primary-color: var(--sicoob-turquesa);
            --secondary-color: var(--sicoob-verde-escuro);
            --accent-color: var(--sicoob-verde-medio);
            --light-color: #F8FFFE;
        }

        body {
            background: linear-gradient(135deg, var(--light-color) 0%, rgba(0, 160, 145, 0.03) 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }

        .navbar {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            box-shadow: 0 4px 20px rgba(0, 54, 65, 0.2);
            border-bottom: 3px solid var(--accent-color);
            padding: 15px 0;
        }

        .navbar-brand {
            font-size: 1.5rem;
            font-weight: 600;
            color: white !important;
        }

        .btn-menu-sicoob {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 8px 16px;
            border-radius: 8px;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .btn-menu-sicoob:hover {
            background: rgba(255, 255, 255, 0.3);
            color: white;
            transform: translateY(-1px);
            text-decoration: none;
        }

        .form-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin: 2rem auto;
            max-width: 1200px;
            overflow: hidden;
        }

        .form-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 1.5rem;
            text-align: center;
        }

        .form-body {
            padding: 2rem;
        }

        .btn-sicoob {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-sicoob:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            color: white;
        }

        .form-label {
            font-weight: 600;
            color: var(--secondary-color);
            margin-bottom: 0.5rem;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(0, 160, 145, 0.25);
        }

        .section-title {
            color: var(--secondary-color);
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 0.5rem;
            margin-bottom: 1.5rem;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid px-4">
            <div class="d-flex justify-content-between align-items-center w-100">
                <div class="d-flex align-items-center">
                    <img src="../assets/images/logo-sicoob.png" alt="Sicoob" class="navbar-brand-img me-3" style="height: 40px; filter: brightness(0) invert(1);">
                    <span class="navbar-brand mb-0 h1">
                        <i class="fas fa-edit me-2"></i>Editar Plano
                    </span>
                </div>
                <div class="d-flex align-items-center">
                    <span class="text-white me-3">
                        <i class="fas fa-user-circle me-1"></i>
                        Olá, <?php echo htmlspecialchars($usuario['nome_completo'] ?? $usuario['username'] ?? 'Usuário'); ?>
                    </span>
                    <a href="visualizar.php?id=<?php echo $plano_id; ?>" class="btn btn-menu-sicoob me-2">
                        <i class="fas fa-arrow-left me-2"></i>Voltar
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Conteúdo Principal -->
    <div class="container-fluid">
        <div class="form-container">
            <div class="form-header">
                <h2 class="mb-0">
                    <i class="fas fa-edit me-2"></i>
                    Editar Plano: <?php echo htmlspecialchars($plano['nome']); ?>
                </h2>
            </div>
            
            <div class="form-body">
                <?php if (isset($erro)): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo htmlspecialchars($erro); ?>
                    </div>
                <?php endif; ?>

                <form method="POST" action="">
                    <!-- Informações Básicas -->
                    <h4 class="section-title">
                        <i class="fas fa-info-circle me-2"></i>Informações Básicas
                    </h4>
                    
                    <div class="row mb-4">
                        <div class="col-md-8">
                            <label for="nome" class="form-label">Nome do Plano *</label>
                            <input type="text" class="form-control" id="nome" name="nome"
                                   value="<?php echo htmlspecialchars($plano['nome']); ?>" required>
                        </div>
                        <div class="col-md-4">
                            <label for="legenda_id" class="form-label">
                                <i class="fas fa-bullseye me-1"></i>Objetivo Estratégico
                            </label>
                            <select class="form-select" id="legenda_id" name="legenda_id">
                                <option value="">Selecione a legenda</option>
                                <?php foreach ($legendas_list as $legenda): ?>
                                <option value="<?php echo $legenda['id']; ?>"
                                        <?php echo ($plano['legenda_id'] == $legenda['id']) ? 'selected' : ''; ?>
                                        data-cor="<?php echo $legenda['cor']; ?>">
                                    <?php echo htmlspecialchars($legenda['nome']); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                Conecte seu plano a um objetivo estratégico
                            </div>
                        </div>
                    </div>

                    <!-- Metodologia 5W2H -->
                    <h4 class="section-title">
                        <i class="fas fa-tasks me-2"></i>Metodologia 5W2H
                    </h4>
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <label for="oque" class="form-label">O QUE será feito? *</label>
                            <textarea class="form-control" id="oque" name="oque" rows="4" required><?php echo htmlspecialchars($plano['oque']); ?></textarea>
                        </div>
                        <div class="col-md-6">
                            <label for="porque" class="form-label">POR QUE fazer? *</label>
                            <textarea class="form-control" id="porque" name="porque" rows="4" required><?php echo htmlspecialchars($plano['porque']); ?></textarea>
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <label for="onde" class="form-label">ONDE será executado?</label>
                            <input type="text" class="form-control" id="onde" name="onde" 
                                   value="<?php echo htmlspecialchars($plano['onde']); ?>">
                        </div>
                        <div class="col-md-6">
                            <label for="como" class="form-label">COMO será executado? *</label>
                            <textarea class="form-control" id="como" name="como" rows="4" required><?php echo htmlspecialchars($plano['como']); ?></textarea>
                        </div>
                    </div>
                    
                    <!-- Cronograma e Custo -->
                    <h4 class="section-title">
                        <i class="fas fa-calendar-alt me-2"></i>Cronograma e Custo
                    </h4>
                    
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <label for="quando_inicio" class="form-label">QUANDO inicia? *</label>
                            <input type="date" class="form-control" id="quando_inicio" name="quando_inicio" 
                                   value="<?php echo $plano['quando_inicio']; ?>" required>
                        </div>
                        <div class="col-md-4">
                            <label for="quando_fim" class="form-label">QUANDO termina? *</label>
                            <input type="date" class="form-control" id="quando_fim" name="quando_fim" 
                                   value="<?php echo $plano['quando_fim']; ?>" required>
                        </div>
                        <div class="col-md-4">
                            <label for="quanto_custa" class="form-label">QUANTO custa?</label>
                            <div class="input-group">
                                <span class="input-group-text">R$</span>
                                <input type="text" class="form-control" id="quanto_custa" name="quanto_custa" 
                                       value="<?php echo number_format($plano['quanto_custa'], 2, ',', '.'); ?>" 
                                       placeholder="0,00">
                            </div>
                        </div>
                    </div>
                    
                    <!-- Resultados -->
                    <h4 class="section-title">
                        <i class="fas fa-chart-bar me-2"></i>Resultados
                    </h4>
                    
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <label for="resultados" class="form-label">Resultados Obtidos</label>
                            <textarea class="form-control" id="resultados" name="resultados" rows="4" 
                                      placeholder="Descreva os resultados obtidos até o momento..."><?php echo htmlspecialchars($plano['resultados']); ?></textarea>
                        </div>
                    </div>
                    
                    <!-- Botões -->
                    <div class="d-flex justify-content-between">
                        <a href="visualizar.php?id=<?php echo $plano_id; ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>Cancelar
                        </a>
                        <button type="submit" name="salvar_plano" class="btn btn-sicoob">
                            <i class="fas fa-save me-2"></i>Salvar Alterações
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Máscara para valor monetário
        document.getElementById('quanto_custa').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            value = (value / 100).toFixed(2) + '';
            value = value.replace(".", ",");
            value = value.replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1.");
            e.target.value = value;
        });
    </script>
</body>
</html>
