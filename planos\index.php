<?php
require_once '../auth_check.php';
require_once '../config/database.php';

// Busca informações do usuário
$stmt = $pdo->prepare("
    SELECT u.*, us.setor_id, u.nivel_acesso_id, u.acesso_gestao_metas, u.acesso_solicitacoes 
    FROM usuarios u 
    LEFT JOIN usuario_setor us ON u.id = us.usuario_id 
    WHERE u.id = ?
");
$stmt->execute([$_SESSION['user_id']]);
$usuario = $stmt->fetch(PDO::FETCH_ASSOC);

// Verificar se o usuário tem permissão para acessar planos de ação
// Por enquanto, vamos permitir acesso para usuários logados
// TODO: Implementar sistema de permissões específico para planos quando necessário
$tem_permissao = true;

// Verificação alternativa: usuários com nível de acesso 1 (admin) ou 2 (gestor) têm acesso
if ($usuario['nivel_acesso_id'] && in_array($usuario['nivel_acesso_id'], [1, 2])) {
    $tem_permissao = true;
} else {
    // Verificar permissões de acesso aos planos (usuário direto ou por setor)
    $tem_permissao = false;

    try {
        // 1. Verificar se tem acesso direto por usuário a qualquer botão
        $stmt = $pdo->prepare("
            SELECT COUNT(*)
            FROM card_button_usuarios cbu
            WHERE cbu.usuario_id = ?
        ");
        $stmt->execute([$_SESSION['user_id']]);
        $tem_permissao_usuario = $stmt->fetchColumn() > 0;

        if ($tem_permissao_usuario) {
            $tem_permissao = true;
        } else {
            // 2. Verificar se tem acesso por setor a qualquer botão
            $stmt = $pdo->prepare("
                SELECT COUNT(*)
                FROM card_button_setores cbs
                INNER JOIN usuario_setor us ON cbs.setor_id = us.setor_id
                WHERE us.usuario_id = ?
            ");
            $stmt->execute([$_SESSION['user_id']]);
            $tem_permissao_setor = $stmt->fetchColumn() > 0;

            if ($tem_permissao_setor) {
                $tem_permissao = true;
            }
        }

    } catch (Exception $e) {
        // Se der erro, permitir acesso para usuários logados
        $tem_permissao = true;
    }
}

// Se não tem permissão, redirecionar
if (!$tem_permissao) {
    header("Location: ../dashboard.php?erro=sem_permissao");
    exit();
}

// Verificar se o usuário é administrador
$is_admin = in_array($usuario['nivel_acesso_id'], [1, 2]);

// Parâmetros de filtro da URL
$filtro_tipo = $_GET['filtro'] ?? '';

// Construir filtro baseado no usuário
$filtro_usuario = "";
$filtro_status = "";
$params_stats = [];
$params_planos = [];

if (!$is_admin) {
    // Para usuários não-admin, mostrar apenas planos onde é responsável
    $filtro_usuario = " WHERE p.id IN (
        SELECT DISTINCT pr.plano_id
        FROM planos_responsaveis pr
        WHERE (pr.tipo = 'usuario' AND pr.referencia_id = ?)
        OR (pr.tipo = 'grupo' AND pr.referencia_id IN (
            SELECT pgu.grupo_id
            FROM planos_grupos_usuarios pgu
            WHERE pgu.usuario_id = ?
        ))
    )";
    $params_stats = [$_SESSION['user_id'], $_SESSION['user_id']];
    $params_planos = [$_SESSION['user_id'], $_SESSION['user_id']];
}

// Adicionar filtro de status baseado no parâmetro da URL
switch ($filtro_tipo) {
    case 'ativos':
        // Status: Em Andamento (2), Iniciado (1), etc. - excluir apenas Concluído (4) e Cancelado
        $filtro_status = ($filtro_usuario ? " AND" : " WHERE") . " p.status_id NOT IN (4, 5)";
        break;
    case 'pendentes':
        // Status: Planejamento (1), Aguardando Aprovação, etc. - não iniciados efetivamente
        $filtro_status = ($filtro_usuario ? " AND" : " WHERE") . " p.status_id IN (1, 3)";
        break;
    case 'concluidos':
        // Status: Concluído (4)
        $filtro_status = ($filtro_usuario ? " AND" : " WHERE") . " p.status_id = 4";
        break;
}

// Buscar estatísticas dos planos
$stats = [
    'total' => 0,
    'em_andamento' => 0,
    'concluidos' => 0,
    'taxa_sucesso' => 0
];

try {
    // Total de planos (com filtro se aplicável)
    $sql_total = "SELECT COUNT(*) FROM planos p" . $filtro_usuario . $filtro_status;
    $stmt = $pdo->prepare($sql_total);
    $stmt->execute($params_stats);
    $stats['total'] = $stmt->fetchColumn();

    // Planos em andamento (status 2 = Em Andamento) - apenas se não há filtro específico
    if (empty($filtro_status)) {
        $sql_andamento = "SELECT COUNT(*) FROM planos p" . $filtro_usuario . ($filtro_usuario ? " AND" : " WHERE") . " p.status_id = 2";
        $stmt = $pdo->prepare($sql_andamento);
        $stmt->execute(array_merge($params_stats, [2]));
        $stats['em_andamento'] = $stmt->fetchColumn();

        // Planos concluídos (status 4 = Concluído)
        $sql_concluidos = "SELECT COUNT(*) FROM planos p" . $filtro_usuario . ($filtro_usuario ? " AND" : " WHERE") . " p.status_id = 4";
        $stmt = $pdo->prepare($sql_concluidos);
        $stmt->execute(array_merge($params_stats, [4]));
        $stats['concluidos'] = $stmt->fetchColumn();
    } else {
        // Se há filtro, mostrar apenas o total filtrado
        $stats['em_andamento'] = 0;
        $stats['concluidos'] = 0;
    }

    // Taxa de sucesso
    if ($stats['total'] > 0) {
        $stats['taxa_sucesso'] = round(($stats['concluidos'] / $stats['total']) * 100, 1);
    }
} catch (Exception $e) {
    // Em caso de erro, manter valores padrão
}

// Buscar planos para exibição
$planos = [];
try {
    $sql_planos = "
        SELECT p.*, ps.nome as status_nome, ps.cor as status_cor, ps.icone as status_icone,
               u.nome_completo as criado_por_nome,
               pl.nome as legenda_nome, pl.cor as legenda_cor,
               DATEDIFF(p.quando_fim, CURDATE()) as dias_restantes
        FROM planos p
        LEFT JOIN planos_status ps ON p.status_id = ps.id
        LEFT JOIN usuarios u ON p.criado_por = u.id
        LEFT JOIN planos_legendas pl ON p.legenda_id = pl.id
        " . $filtro_usuario . $filtro_status . "
        ORDER BY p.created_at DESC
        LIMIT 20
    ";
    $stmt = $pdo->prepare($sql_planos);
    $stmt->execute($params_planos);
    $planos = $stmt->fetchAll();
} catch (Exception $e) {
    // Em caso de erro, manter array vazio
}

// Verificar mensagens de toast
$toast_message = '';
$toast_type = '';

if (isset($_GET['success'])) {
    switch ($_GET['success']) {
        case 'plano_criado':
            $toast_message = 'Plano de ação criado com sucesso!';
            $toast_type = 'success';
            break;
        case 'plano_excluido':
            $toast_message = 'Plano de ação excluído com sucesso!';
            $toast_type = 'success';
            break;
    }
}

if (isset($_GET['error'])) {
    $toast_message = 'Erro: ' . htmlspecialchars($_GET['error']);
    $toast_type = 'error';
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Planos de Ação - Sistema Sicoob</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="../assets/images/Sicoob.ico">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            /* Paleta de Cores Oficial Sicoob 2024 - Manual de Marca */
            --sicoob-turquesa: #00A091;        /* RGB: 0, 160, 145 - Cor principal */
            --sicoob-verde-escuro: #003641;    /* RGB: 0, 54, 65 - Verde escuro */
            --sicoob-verde-medio: #7DB61C;     /* RGB: 125, 182, 28 - Verde médio */
            --sicoob-verde-claro: #C9D200;     /* RGB: 201, 210, 0 - Verde claro */
            --sicoob-roxo: #49479D;            /* RGB: 73, 71, 157 - Roxo */
            --sicoob-branco: #FFFFFF;          /* RGB: 255, 255, 255 - Branco */

            /* Cores de Sistema baseadas na identidade oficial */
            --primary-color: var(--sicoob-turquesa);
            --secondary-color: var(--sicoob-verde-escuro);
            --accent-color: var(--sicoob-verde-medio);
            --accent-light: var(--sicoob-verde-claro);
            --accent-purple: var(--sicoob-roxo);
            --success-color: var(--sicoob-verde-medio);
            --warning-color: var(--sicoob-verde-claro);
            --danger-color: #D32F2F;
            --info-color: var(--sicoob-turquesa);
            --dark-color: var(--sicoob-verde-escuro);
            --light-color: #F8FFFE;
            --white-color: var(--sicoob-branco);
            --gray-color: #6B7280;
        }

        /* Layout e Estrutura - Identidade Visual Sicoob Oficial */
        body {
            background: linear-gradient(135deg, var(--light-color) 0%, rgba(0, 160, 145, 0.03) 100%);
            position: relative;
            font-family: 'Open Sans', sans-serif;
            min-height: 100vh;
        }

        /* Navbar - Identidade Visual Oficial Sicoob */
        .navbar {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            box-shadow: 0 4px 20px rgba(0, 54, 65, 0.2);
            border-bottom: 3px solid var(--accent-color);
            position: relative;
            padding: 15px 0;
        }

        .navbar-brand {
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0;
            color: white !important;
        }

        .navbar-brand-img {
            filter: brightness(1.1);
        }

        .btn-outline-light {
            border-color: rgba(255,255,255,0.5);
            color: white;
            transition: all 0.3s ease;
        }

        .btn-outline-light:hover {
            background-color: rgba(255,255,255,0.2);
            border-color: white;
            color: white;
            transform: translateY(-1px);
        }

        /* Elemento gráfico sutil de fundo inspirado nos padrões oficiais Sicoob */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            right: 0;
            width: 300px;
            height: 100vh;
            background: linear-gradient(60deg,
                transparent 0%,
                rgba(0, 160, 145, 0.015) 20%,
                rgba(125, 182, 28, 0.01) 40%,
                rgba(201, 210, 0, 0.008) 60%,
                rgba(73, 71, 157, 0.005) 80%,
                transparent 100%);
            z-index: -1;
            opacity: 0.7;
        }

        /* Container principal */
        .main-container {
            padding: 30px 0;
            min-height: calc(100vh - 120px);
        }

        /* Cards de planos */
        .plano-card {
            background: var(--white-color);
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0, 54, 65, 0.1);
            border: 1px solid rgba(0, 160, 145, 0.1);
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .plano-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 54, 65, 0.15);
        }

        .plano-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .plano-header h3 {
            margin: 0;
            font-weight: 600;
        }

        .plano-body {
            padding: 25px;
        }

        /* Estilos da tabela de planos */
        .table-responsive {
            border-radius: 10px;
            overflow: hidden;
        }

        .table {
            margin-bottom: 0;
        }

        .table thead th {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            border: none;
            font-weight: 600;
            padding: 15px 12px;
            font-size: 0.9rem;
        }

        .plano-card-item {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 1rem;
            overflow: hidden;
            transition: all 0.3s ease;
            cursor: pointer;
            border: 1px solid #e9ecef;
        }

        .plano-card-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
            border-color: var(--primary-color);
        }

        .plano-card-body {
            padding: 1.25rem;
            min-height: 140px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .plano-title {
            font-size: 1.1rem;
            font-weight: 700;
            margin-bottom: 0.75rem;
            color: var(--secondary-color);
            line-height: 1.3;
        }

        .plano-status {
            padding: 0.4rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            margin-bottom: 0.75rem;
        }

        .plano-legenda {
            padding: 0.4rem 0.8rem;
            border-radius: 15px;
            font-size: 0.75rem;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            white-space: nowrap;
            opacity: 0.9;
        }

        .plano-periodo {
            font-size: 0.85rem;
            color: var(--gray-color);
            line-height: 1.4;
        }

        .plano-periodo strong {
            color: var(--secondary-color);
        }

        .table tbody tr {
            transition: all 0.2s ease;
        }

        .table tbody tr:hover {
            background-color: rgba(0, 160, 145, 0.05);
        }

        .table tbody td {
            padding: 15px 12px;
            vertical-align: middle;
            border-color: rgba(0, 160, 145, 0.1);
        }

        .btn-group-sm .btn {
            padding: 4px 8px;
            font-size: 0.8rem;
        }

        /* Toast Notifications */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
        }

        .toast-sicoob {
            min-width: 350px;
            border: none;
            border-radius: 10px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(10px);
        }

        .toast-sicoob.success {
            background: linear-gradient(135deg, var(--success-color) 0%, var(--accent-color) 100%);
            color: white;
        }

        .toast-sicoob.error {
            background: linear-gradient(135deg, var(--danger-color) 0%, #B71C1C 100%);
            color: white;
        }

        .toast-sicoob .toast-header {
            background: rgba(255, 255, 255, 0.1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            color: inherit;
        }

        .toast-sicoob .toast-body {
            font-weight: 500;
        }

        .toast-sicoob .btn-close {
            filter: invert(1);
        }

        /* Botões de ação */
        .btn-sicoob {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-sicoob:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 160, 145, 0.3);
            color: white;
        }

        .btn-outline-sicoob {
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
            background: transparent;
            padding: 10px 20px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-outline-sicoob:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }

        /* Estatísticas */
        .stats-card {
            background: var(--white-color);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0, 54, 65, 0.08);
            border-left: 4px solid var(--primary-color);
        }

        .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin: 0;
        }

        .stats-label {
            color: var(--gray-color);
            font-size: 0.9rem;
            margin-top: 5px;
        }

        /* === ESTILO DO BOTÃO MENU (IGUAL AO DASHBOARD) === */
        .btn-menu-sicoob {
            background: rgba(255, 255, 255, 0.15);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .btn-menu-sicoob:hover {
            background: rgba(255, 255, 255, 0.25);
            border-color: rgba(255, 255, 255, 0.5);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        .btn-menu-sicoob:active {
            transform: translateY(0);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .btn-menu-sicoob i {
            font-size: 0.9rem;
        }

        /* Offcanvas Personalizado Sicoob */
        .offcanvas-sicoob {
            border-left: 4px solid var(--sicoob-turquesa);
            width: 350px !important;
        }

        .offcanvas-sicoob .offcanvas-header {
            background: linear-gradient(135deg, var(--sicoob-turquesa) 0%, var(--sicoob-verde-escuro) 100%);
            color: white;
            padding: 20px;
        }

        .offcanvas-sicoob .offcanvas-title {
            font-size: 1.3rem;
            font-weight: 600;
        }

        /* Lista de Grupos Personalizada */
        .list-group-item-sicoob {
            border: none;
            padding: 15px 20px;
            margin-bottom: 8px;
            border-radius: 12px;
            background: rgba(0, 160, 145, 0.05);
            border-left: 4px solid transparent;
            transition: all 0.3s ease;
        }

        .list-group-item-sicoob:hover {
            background: rgba(0, 160, 145, 0.1);
            border-left-color: var(--sicoob-turquesa);
            transform: translateX(5px);
        }

        .list-group-item-sicoob.active {
            background: linear-gradient(135deg, var(--sicoob-turquesa) 0%, var(--sicoob-verde-medio) 100%);
            border-left-color: var(--sicoob-turquesa);
            color: white;
            transform: translateX(5px);
        }

        .list-group-item-sicoob.active .menu-item-content h6,
        .list-group-item-sicoob.active .menu-item-content p {
            color: white;
        }

        .list-group-item-sicoob.active .menu-item-icon {
            background: rgba(255, 255, 255, 0.2) !important;
            color: white !important;
        }

        /* Estilo especial para o botão de limpeza */
        .list-group-item-sicoob[href*="limpar_dados_teste"]:hover {
            background: rgba(220, 53, 69, 0.1);
            border-left-color: #dc3545;
            transform: translateX(5px);
        }

        .list-group-item-sicoob[href*="limpar_dados_teste"]:hover .menu-item-content h6 {
            color: #dc3545 !important;
        }

        .menu-item-icon {
            width: 45px;
            height: 45px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 1.2rem;
        }

        .menu-item-content h6 {
            margin: 0;
            font-size: 1rem;
            font-weight: 600;
            color: var(--sicoob-verde-escuro);
        }

        .menu-item-content p {
            margin: 0;
            font-size: 0.85rem;
            color: var(--gray-color);
        }

        /* Seção de categorias no offcanvas */
        .offcanvas-category {
            margin-bottom: 25px;
        }

        .offcanvas-category-title {
            color: var(--sicoob-verde-escuro);
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid rgba(0, 160, 145, 0.2);
            display: flex;
            align-items: center;
        }

        .offcanvas-category-title i {
            margin-right: 10px;
            color: var(--sicoob-turquesa);
        }

        /* Responsividade */
        @media (max-width: 768px) {
            .main-container {
                padding: 20px 0;
            }

            .plano-body {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar - Identidade Visual Oficial Sicoob -->
    <nav class="navbar navbar-expand-lg">
        <div class="container-fluid px-4">
            <div class="d-flex justify-content-between align-items-center w-100">
                <!-- Logo e Título à Esquerda -->
                <div class="d-flex align-items-center">
                    <img src="../assets/images/logo-sicoob.png" alt="Sicoob" class="navbar-brand-img me-3" style="height: 40px;">
                    <span class="navbar-brand mb-0 h1 text-white">
                        <i class="fas fa-tasks me-2"></i>Planos de Ação
                        <?php if (!empty($filtro_tipo)): ?>
                        <span class="badge bg-light text-dark ms-2" style="font-size: 0.5em; vertical-align: middle;">
                            <?php
                            switch($filtro_tipo) {
                                case 'ativos': echo 'ATIVOS'; break;
                                case 'pendentes': echo 'PENDENTES'; break;
                                case 'concluidos': echo 'CONCLUÍDOS'; break;
                            }
                            ?>
                        </span>
                        <?php endif; ?>
                    </span>
                </div>

                <!-- Usuário e Menu à Direita -->
                <div class="d-flex align-items-center">
                    <span class="text-white me-3">
                        <i class="fas fa-user-circle me-1"></i>
                        Olá, <?php echo htmlspecialchars($usuario['nome_completo']); ?>
                    </span>

                    <!-- Botão Menu Offcanvas (Estilizado) -->
                    <button class="btn btn-menu-sicoob" type="button"
                            data-bs-toggle="offcanvas" data-bs-target="#menuOffcanvas"
                            aria-controls="menuOffcanvas" title="Menu de Navegação">
                        <i class="fas fa-bars me-2"></i>
                        <span>Menu</span>
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Offcanvas Menu Sicoob -->
    <div class="offcanvas offcanvas-end offcanvas-sicoob" tabindex="-1" id="menuOffcanvas" aria-labelledby="menuOffcanvasLabel">
        <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="menuOffcanvasLabel">
                <i class="fas fa-tasks me-2"></i>Menu Planos de Ação
            </h5>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="offcanvas" aria-label="Close"></button>
        </div>
        <div class="offcanvas-body">
            <p class="text-muted mb-4">Acesse rapidamente as funcionalidades do sistema de planos de ação.</p>
            <!-- Seção Ações Rápidas -->
            <div class="offcanvas-category">
                <div class="offcanvas-category-title">
                    <i class="fas fa-plus-circle"></i>
                    Ações Rápidas
                </div>
                <div class="list-group list-group-flush">
                    <a href="novo.php" class="list-group-item list-group-item-action list-group-item-sicoob">
                        <div class="d-flex align-items-center">
                            <div class="menu-item-icon" style="background: rgba(0, 160, 145, 0.1); color: var(--sicoob-turquesa);">
                                <i class="fas fa-plus"></i>
                            </div>
                            <div class="menu-item-content">
                                <h6>Novo Plano</h6>
                                <p>Criar um novo plano de ação</p>
                            </div>
                        </div>
                    </a>
                </div>
            </div>

            <!-- Seção Administração (apenas para administradores) -->
            <?php if ($is_admin): ?>
            <div class="offcanvas-category">
                <div class="offcanvas-category-title">
                    <i class="fas fa-cogs"></i>
                    Administração
                </div>
                <div class="list-group list-group-flush">
                    <a href="admin_status.php" class="list-group-item list-group-item-action list-group-item-sicoob">
                        <div class="d-flex align-items-center">
                            <div class="menu-item-icon" style="background: rgba(0, 160, 145, 0.1); color: var(--sicoob-turquesa);">
                                <i class="fas fa-cogs"></i>
                            </div>
                            <div class="menu-item-content">
                                <h6>Gerenciar Status</h6>
                                <p>Administrar status dos planos</p>
                            </div>
                        </div>
                    </a>
                    <a href="admin_legendas.php" class="list-group-item list-group-item-action list-group-item-sicoob">
                        <div class="d-flex align-items-center">
                            <div class="menu-item-icon" style="background: rgba(125, 182, 28, 0.1); color: var(--sicoob-verde-medio);">
                                <i class="fas fa-bullseye"></i>
                            </div>
                            <div class="menu-item-content">
                                <h6>Objetivos Estratégicos</h6>
                                <p>Gerenciar legendas dos planos</p>
                            </div>
                        </div>
                    </a>
                    <a href="admin_grupos.php" class="list-group-item list-group-item-action list-group-item-sicoob">
                        <div class="d-flex align-items-center">
                            <div class="menu-item-icon" style="background: rgba(73, 71, 157, 0.1); color: var(--sicoob-roxo);">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="menu-item-content">
                                <h6>Gerenciar Grupos</h6>
                                <p>Administrar grupos de usuários</p>
                            </div>
                        </div>
                    </a>
                    <a href="limpar_dados_teste.php" class="list-group-item list-group-item-action list-group-item-sicoob" style="border-left-color: #dc3545;">
                        <div class="d-flex align-items-center">
                            <div class="menu-item-icon" style="background: rgba(220, 53, 69, 0.1); color: #dc3545;">
                                <i class="fas fa-trash-alt"></i>
                            </div>
                            <div class="menu-item-content">
                                <h6 style="color: #dc3545;">Limpar Dados de Teste</h6>
                                <p>Remover todos os planos (CUIDADO!)</p>
                            </div>
                        </div>
                    </a>

                </div>
            </div>
            <?php endif; ?>

            <!-- Seção Navegação -->
            <div class="offcanvas-category">
                <div class="offcanvas-category-title">
                    <i class="fas fa-compass"></i>
                    Navegação
                </div>
                <div class="list-group list-group-flush">
                    <a href="../dashboard.php" class="list-group-item list-group-item-action list-group-item-sicoob">
                        <div class="d-flex align-items-center">
                            <div class="menu-item-icon" style="background: rgba(0, 54, 65, 0.1); color: var(--sicoob-verde-escuro);">
                                <i class="fas fa-home"></i>
                            </div>
                            <div class="menu-item-content">
                                <h6>Dashboard Principal</h6>
                                <p>Voltar ao dashboard principal</p>
                            </div>
                        </div>
                    </a>
                </div>
            </div>

            <!-- Seção Filtros -->
            <div class="offcanvas-category">
                <div class="offcanvas-category-title">
                    <i class="fas fa-filter"></i>
                    Filtros Rápidos
                </div>
                <div class="list-group list-group-flush">
                    <a href="index.php" class="list-group-item list-group-item-action list-group-item-sicoob <?php echo (empty($filtro_tipo)) ? 'active' : ''; ?>">
                        <div class="d-flex align-items-center">
                            <div class="menu-item-icon" style="background: rgba(0, 54, 65, 0.1); color: var(--sicoob-azul-escuro);">
                                <i class="fas fa-list"></i>
                            </div>
                            <div class="menu-item-content">
                                <h6>Todos os Planos</h6>
                                <p>Ver todos os planos</p>
                            </div>
                        </div>
                    </a>
                    <a href="index.php?filtro=ativos" class="list-group-item list-group-item-action list-group-item-sicoob <?php echo ($filtro_tipo == 'ativos') ? 'active' : ''; ?>">
                        <div class="d-flex align-items-center">
                            <div class="menu-item-icon" style="background: rgba(0, 160, 145, 0.1); color: var(--sicoob-turquesa);">
                                <i class="fas fa-play-circle"></i>
                            </div>
                            <div class="menu-item-content">
                                <h6>Planos Ativos</h6>
                                <p>Filtrar planos em andamento</p>
                            </div>
                        </div>
                    </a>
                    <a href="index.php?filtro=pendentes" class="list-group-item list-group-item-action list-group-item-sicoob <?php echo ($filtro_tipo == 'pendentes') ? 'active' : ''; ?>">
                        <div class="d-flex align-items-center">
                            <div class="menu-item-icon" style="background: rgba(201, 210, 0, 0.1); color: var(--sicoob-verde-claro);">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="menu-item-content">
                                <h6>Pendentes</h6>
                                <p>Planos aguardando ação</p>
                            </div>
                        </div>
                    </a>
                    <a href="index.php?filtro=concluidos" class="list-group-item list-group-item-action list-group-item-sicoob <?php echo ($filtro_tipo == 'concluidos') ? 'active' : ''; ?>">
                        <div class="d-flex align-items-center">
                            <div class="menu-item-icon" style="background: rgba(125, 182, 28, 0.1); color: var(--sicoob-verde-medio);">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="menu-item-content">
                                <h6>Concluídos</h6>
                                <p>Planos finalizados</p>
                            </div>
                        </div>
                    </a>
                </div>
            </div>

            <!-- Rodapé -->
            <div class="mt-4 pt-3" style="border-top: 1px solid rgba(0, 160, 145, 0.2);">
                <div class="text-center">
                    <small class="text-muted">
                        <i class="fas fa-shield-alt me-1"></i>
                        Sistema Sicoob - Planos de Ação
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Conteúdo Principal -->
    <div class="main-container">
        <div class="container-fluid">


            <!-- Área de Planos -->
            <div class="row">
                <div class="col-12">
                    <?php if (empty($planos)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-clipboard-list fa-4x mb-3" style="color: var(--gray-color); opacity: 0.3;"></i>
                                <h4 style="color: var(--gray-color);">Nenhum plano de ação encontrado</h4>
                                <p class="text-muted mb-4">Comece criando seu primeiro plano de ação estratégico</p>
                                <a href="novo.php" class="btn btn-sicoob">
                                    <i class="fas fa-plus me-2"></i>Criar Primeiro Plano
                                </a>
                            </div>
                            <?php else: ?>
                            <!-- Botão Novo Plano -->
                            <div class="d-flex justify-content-end mb-4">
                                <a href="novo.php" class="btn btn-sicoob">
                                    <i class="fas fa-plus me-2"></i>Novo Plano
                                </a>
                            </div>

                            <!-- Grid de Cards dos Planos -->
                            <div class="row">
                                <?php foreach ($planos as $plano): ?>
                                <div class="col-lg-4 col-md-6">
                                    <div class="plano-card-item" data-plano-id="<?php echo $plano['id']; ?>">
                                        <div class="plano-card-body">
                                            <!-- Nome do Plano -->
                                            <div class="plano-title">
                                                <?php echo htmlspecialchars($plano['nome']); ?>
                                            </div>

                                            <!-- Status -->
                                            <div class="mb-3">
                                                <span class="plano-status" style="background-color: <?php echo $plano['status_cor']; ?>; color: white;">
                                                    <i class="<?php echo $plano['status_icone']; ?> me-1"></i>
                                                    <?php echo htmlspecialchars($plano['status_nome']); ?>
                                                </span>

                                                <?php if (!empty($plano['legenda_nome'])): ?>
                                                <span class="plano-legenda ms-2" style="background-color: <?php echo $plano['legenda_cor']; ?>; color: white;">
                                                    <i class="fas fa-bullseye me-1"></i>
                                                    <?php echo htmlspecialchars($plano['legenda_nome']); ?>
                                                </span>
                                                <?php endif; ?>
                                            </div>

                                            <!-- Período -->
                                            <div class="plano-periodo">
                                                <div class="mb-1">
                                                    <i class="fas fa-calendar-alt me-1"></i>
                                                    <strong>Início:</strong> <?php echo date('d/m/Y', strtotime($plano['quando_inicio'])); ?>
                                                </div>
                                                <div>
                                                    <i class="fas fa-calendar-check me-1"></i>
                                                    <strong>Fim:</strong> <?php echo date('d/m/Y', strtotime($plano['quando_fim'])); ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                            <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div class="toast-container">
        <?php if ($toast_message): ?>
        <div class="toast toast-sicoob <?php echo $toast_type; ?>" id="toastNotification" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <i class="fas <?php echo ($toast_type == 'success') ? 'fa-check-circle' : 'fa-exclamation-triangle'; ?> me-2"></i>
                <strong class="me-auto">
                    <?php echo ($toast_type == 'success') ? 'Sucesso' : 'Erro'; ?>
                </strong>
                <small>agora</small>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body">
                <?php echo $toast_message; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Inicialização
        document.addEventListener('DOMContentLoaded', function() {
            // Exibir toast se existir
            const toast = document.getElementById('toastNotification');
            if (toast) {
                const bsToast = new bootstrap.Toast(toast, {
                    autohide: true,
                    delay: 5000
                });
                bsToast.show();

                // Limpar URL após exibir o toast
                setTimeout(() => {
                    const url = new URL(window.location);
                    url.searchParams.delete('success');
                    url.searchParams.delete('error');
                    url.searchParams.delete('plano_id');
                    window.history.replaceState({}, document.title, url);
                }, 1000);
            }
        });

        // Tornar cards clicáveis
        document.querySelectorAll('.plano-card-item').forEach(card => {
            card.addEventListener('click', function() {
                const planoId = this.getAttribute('data-plano-id');
                window.location.href = `visualizar.php?id=${planoId}`;
            });
        });

        // Funções do Offcanvas
        // Função removida - agora usa link direto



        // Filtros agora funcionam via links diretos - funções removidas

        // Melhorar experiência do offcanvas (igual ao dashboard)
        document.addEventListener('DOMContentLoaded', function() {
            const offcanvasElement = document.getElementById('menuOffcanvas');
            const menuItems = document.querySelectorAll('.list-group-item-sicoob');

            // Adicionar animação aos itens do menu
            menuItems.forEach(function(item, index) {
                item.style.opacity = '0';
                item.style.transform = 'translateX(20px)';
                item.style.transition = 'all 0.3s ease';
            });

            // Animar itens quando o offcanvas abrir
            offcanvasElement.addEventListener('shown.bs.offcanvas', function() {
                menuItems.forEach(function(item, index) {
                    setTimeout(function() {
                        item.style.opacity = '1';
                        item.style.transform = 'translateX(0)';
                    }, index * 100);
                });
            });

            // Reset animação quando fechar
            offcanvasElement.addEventListener('hidden.bs.offcanvas', function() {
                menuItems.forEach(function(item) {
                    item.style.opacity = '0';
                    item.style.transform = 'translateX(20px)';
                });
            });
        });
    </script>
</body>
</html>
