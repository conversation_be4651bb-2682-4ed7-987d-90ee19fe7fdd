<?php
session_start();
require_once 'config/database.php';
require_once 'planos/PlanosLogger.php';

// Simular usuário logado para teste
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1; // Assumindo que existe um usuário com ID 1
}

echo "<h2>🧪 Teste do Sistema de Logs dos Planos</h2>";

try {
    $logger = new PlanosLogger($pdo);
    
    // 1. Verificar se a tabela de logs existe e tem a estrutura correta
    echo "<h3>📋 Verificando Estrutura da Tabela de Logs</h3>";
    
    $stmt = $pdo->query("DESCRIBE logs");
    $campos = $stmt->fetchAll();
    
    echo "<table class='table table-striped'>";
    echo "<tr><th>Campo</th><th>Tipo</th><th>Nulo</th><th>Chave</th><th><PERSON>dr<PERSON></th></tr>";
    foreach ($campos as $campo) {
        echo "<tr>";
        echo "<td>{$campo['Field']}</td>";
        echo "<td>{$campo['Type']}</td>";
        echo "<td>{$campo['Null']}</td>";
        echo "<td>{$campo['Key']}</td>";
        echo "<td>{$campo['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 2. Testar registro de log simples
    echo "<h3>🔧 Testando Registro de Log</h3>";
    
    $resultado = $logger->registrarLog(
        $_SESSION['user_id'], 
        'teste_sistema', 
        'Teste do sistema de logs dos planos de ação',
        null,
        '127.0.0.1'
    );
    
    if ($resultado) {
        echo "<div class='alert alert-success'>✅ Log de teste registrado com sucesso!</div>";
    } else {
        echo "<div class='alert alert-danger'>❌ Falha ao registrar log de teste</div>";
    }
    
    // 3. Testar log de criação de plano (simulado)
    echo "<h3>📝 Testando Log de Criação de Plano</h3>";
    
    $dados_plano_teste = [
        'nome' => 'Plano de Teste para Logs',
        'status_id' => 1,
        'legenda_id' => 1,
        'quando_inicio' => date('Y-m-d'),
        'quando_fim' => date('Y-m-d', strtotime('+30 days')),
        'quanto_custa' => 1000.00
    ];
    
    $resultado_criacao = $logger->logCriarPlano($_SESSION['user_id'], 999, $dados_plano_teste);
    
    if ($resultado_criacao) {
        echo "<div class='alert alert-success'>✅ Log de criação de plano registrado com sucesso!</div>";
    } else {
        echo "<div class='alert alert-danger'>❌ Falha ao registrar log de criação de plano</div>";
    }
    
    // 4. Testar log de mudança de status
    echo "<h3>🔄 Testando Log de Mudança de Status</h3>";
    
    $resultado_status = $logger->logMudarStatus($_SESSION['user_id'], 999, 1, 2, 'Teste de mudança de status');
    
    if ($resultado_status) {
        echo "<div class='alert alert-success'>✅ Log de mudança de status registrado com sucesso!</div>";
    } else {
        echo "<div class='alert alert-danger'>❌ Falha ao registrar log de mudança de status</div>";
    }
    
    // 5. Testar log administrativo
    echo "<h3>⚙️ Testando Log Administrativo</h3>";
    
    $resultado_admin = $logger->logAcaoAdmin($_SESSION['user_id'], 'teste_admin', [
        'acao' => 'teste',
        'detalhes' => 'Teste de ação administrativa',
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
    if ($resultado_admin) {
        echo "<div class='alert alert-success'>✅ Log administrativo registrado com sucesso!</div>";
    } else {
        echo "<div class='alert alert-danger'>❌ Falha ao registrar log administrativo</div>";
    }
    
    // 6. Buscar e exibir logs recentes
    echo "<h3>📊 Últimos Logs Registrados</h3>";
    
    $stmt = $pdo->query("
        SELECT l.*, u.nome_completo as usuario_nome
        FROM logs l
        LEFT JOIN usuarios u ON l.usuario_id = u.id
        WHERE l.acao LIKE '%plano%' OR l.acao LIKE '%admin_%' OR l.acao LIKE '%teste%'
        ORDER BY l.data_hora DESC
        LIMIT 10
    ");
    $logs_recentes = $stmt->fetchAll();
    
    if (empty($logs_recentes)) {
        echo "<div class='alert alert-info'>ℹ️ Nenhum log encontrado</div>";
    } else {
        echo "<div class='alert alert-info'>📈 " . count($logs_recentes) . " logs encontrados</div>";
        
        echo "<div class='logs-container'>";
        foreach ($logs_recentes as $log) {
            echo "<div class='log-item'>";
            echo "<div class='log-header'>";
            echo "<span class='badge bg-primary'>{$log['acao']}</span>";
            echo "<span class='text-muted ms-2'>" . date('d/m/Y H:i:s', strtotime($log['data_hora'])) . "</span>";
            echo "<span class='text-info ms-2'>👤 {$log['usuario_nome']}</span>";
            if ($log['ip_usuario']) {
                echo "<span class='text-secondary ms-2'>🌐 {$log['ip_usuario']}</span>";
            }
            echo "</div>";
            
            if ($log['detalhes']) {
                echo "<div class='log-details'>";
                $detalhes = $log['detalhes'];
                // Tentar decodificar JSON
                $json_decoded = json_decode($detalhes, true);
                if ($json_decoded) {
                    echo "<pre>" . htmlspecialchars(json_encode($json_decoded, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . "</pre>";
                } else {
                    echo "<p>" . htmlspecialchars($detalhes) . "</p>";
                }
                echo "</div>";
            }
            echo "</div>";
        }
        echo "</div>";
    }
    
    // 7. Estatísticas dos logs
    echo "<h3>📈 Estatísticas dos Logs</h3>";
    
    $stmt = $pdo->query("
        SELECT
            COUNT(*) as total_logs,
            COUNT(DISTINCT usuario_id) as usuarios_ativos,
            DATE(MIN(data_hora)) as primeiro_log,
            DATE(MAX(data_hora)) as ultimo_log
        FROM logs
        WHERE acao LIKE '%plano%' OR acao LIKE '%admin_%'
    ");
    $stats = $stmt->fetch();
    
    echo "<div class='row'>";
    echo "<div class='col-md-3'>";
    echo "<div class='stat-card'>";
    echo "<h4>{$stats['total_logs']}</h4>";
    echo "<p>Total de Logs</p>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='col-md-3'>";
    echo "<div class='stat-card'>";
    echo "<h4>{$stats['usuarios_ativos']}</h4>";
    echo "<p>Usuários Ativos</p>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='col-md-3'>";
    echo "<div class='stat-card'>";
    echo "<h4>" . ($stats['ultimo_log'] ? date('d/m/Y', strtotime($stats['ultimo_log'])) : 'N/A') . "</h4>";
    echo "<p>Último Log</p>";
    echo "</div>";
    echo "</div>";
    
    echo "<div class='col-md-3'>";
    echo "<div class='stat-card'>";
    echo "<h4>" . ($stats['primeiro_log'] ? date('d/m/Y', strtotime($stats['primeiro_log'])) : 'N/A') . "</h4>";
    echo "<p>Primeiro Log</p>";
    echo "</div>";
    echo "</div>";
    echo "</div>";
    
    // 8. Links para testar funcionalidades
    echo "<h3>🔗 Links para Teste</h3>";
    echo "<div class='test-links'>";
    echo "<a href='planos/novo.php' class='btn btn-primary'>🆕 Criar Novo Plano (com log)</a> ";
    echo "<a href='planos/logs.php' class='btn btn-info'>📊 Ver Página de Logs</a> ";
    echo "<a href='planos/admin_status.php' class='btn btn-warning'>⚙️ Admin Status (com log)</a> ";
    echo "<a href='planos/admin_legendas.php' class='btn btn-success'>🎯 Admin Legendas (com log)</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>❌ Erro: " . $e->getMessage() . "</div>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
    background: #f8f9fa;
    line-height: 1.6;
}

h2, h3 {
    color: #003641;
    margin-bottom: 15px;
}

.alert {
    padding: 15px;
    margin: 15px 0;
    border-radius: 8px;
    border: 1px solid transparent;
}

.alert-success {
    background: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.alert-warning {
    background: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.alert-info {
    background: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

.alert-danger {
    background: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.table {
    width: 100%;
    border-collapse: collapse;
    margin: 15px 0;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.table th, .table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.table th {
    background: #003641;
    color: white;
    font-weight: 600;
}

.table-striped tr:nth-child(even) {
    background: #f8f9fa;
}

.logs-container {
    margin: 20px 0;
}

.log-item {
    background: white;
    border-left: 4px solid #00A091;
    padding: 15px;
    margin-bottom: 15px;
    border-radius: 0 8px 8px 0;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.log-header {
    margin-bottom: 10px;
    font-weight: 600;
}

.log-details {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 6px;
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    max-height: 200px;
    overflow-y: auto;
}

.badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.bg-primary {
    background-color: #007bff;
    color: white;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: 20px 0;
}

.col-md-3 {
    flex: 0 0 25%;
    padding: 0 10px;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.stat-card h4 {
    color: #00A091;
    font-size: 2rem;
    margin-bottom: 5px;
}

.stat-card p {
    color: #666;
    margin: 0;
}

.test-links {
    margin: 20px 0;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    margin: 5px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: linear-gradient(135deg, #00A091 0%, #008a7c 100%);
    color: white;
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    color: #212529;
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    color: white;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    text-decoration: none;
}

pre {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
    overflow-x: auto;
    font-size: 0.8rem;
}
</style>
